#!/usr/bin/env python3
"""
Quick Rename Script - Versi sederhana untuk rename cepat
Khusus untuk kasus seperti Anisolabididae 1, 2, 3, dst.
"""

import os
from pathlib import Path

def quick_rename(folder_path, base_name="Anisolabididae"):
    """
    Rename semua file dalam folder dengan format: "base_name 1", "base_name 2", dst.
    
    Args:
        folder_path (str): Path ke folder
        base_name (str): <PERSON><PERSON> dasar (default: "Anisolabididae")
    """
    
    folder = Path(folder_path)
    
    if not folder.exists():
        print(f"❌ Folder '{folder_path}' tidak ditemukan!")
        return
    
    # Dapatkan semua file
    files = [f for f in folder.iterdir() if f.is_file()]
    
    if not files:
        print(f"📁 Folder kosong!")
        return
    
    # Urutkan file
    files.sort(key=lambda x: x.name.lower())
    
    print(f"📁 Folder: {folder}")
    print(f"🏷️  Base name: {base_name}")
    print(f"📊 Total file: {len(files)}")
    print("\n🔄 Memulai rename:")
    print("-" * 50)
    
    for i, file_path in enumerate(files, 1):
        try:
            # Dapatkan ekstensi
            extension = file_path.suffix
            
            # Nama baru
            new_name = f"{base_name} {i}{extension}"
            new_path = folder / new_name
            
            # Rename
            file_path.rename(new_path)
            print(f"  ✅ {file_path.name} → {new_name}")
            
        except Exception as e:
            print(f"  ❌ Error: {file_path.name} - {e}")
    
    print("-" * 50)
    print(f"🎉 Selesai! {len(files)} file telah direname.")

# Contoh penggunaan langsung
if __name__ == "__main__":
    # Ganti path ini sesuai kebutuhan
    folder_paths = [
        "ResizedDatasetUnknown/Anisolabididae",
        "ResizedDatasetUnknown/Carabidae", 
        "ResizedDatasetUnknown/Coccinellidae",
        "ResizedDatasetUnknown/Staphylinidae"
    ]
    
    print("🔄 QUICK RENAME SCRIPT")
    print("=" * 50)
    
    # Pilih folder atau input manual
    print("Pilih folder:")
    for i, path in enumerate(folder_paths, 1):
        if Path(path).exists():
            file_count = len([f for f in Path(path).iterdir() if f.is_file()])
            print(f"  {i}. {path} ({file_count} files)")
        else:
            print(f"  {i}. {path} (tidak ditemukan)")
    
    print(f"  {len(folder_paths)+1}. Input manual")
    
    try:
        choice = int(input("\nPilih (1-{}): ".format(len(folder_paths)+1)))
        
        if 1 <= choice <= len(folder_paths):
            selected_folder = folder_paths[choice-1]
            folder_name = Path(selected_folder).name
            
            print(f"\n📁 Folder terpilih: {selected_folder}")
            base_name = input(f"🏷️  Base name (default: {folder_name}): ").strip()
            if not base_name:
                base_name = folder_name
            
            quick_rename(selected_folder, base_name)
            
        elif choice == len(folder_paths)+1:
            folder_path = input("📁 Masukkan path folder: ").strip().strip('"')
            base_name = input("🏷️  Base name: ").strip()
            
            if not base_name:
                base_name = Path(folder_path).name
            
            quick_rename(folder_path, base_name)
        else:
            print("❌ Pilihan tidak valid!")
            
    except (ValueError, KeyboardInterrupt):
        print("\n❌ Program dibatalkan.")
