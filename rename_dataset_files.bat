@echo off
echo ========================================
echo SCRIPT RENAME FILE DATASET
echo ========================================
echo.

REM Rename files in Lonchura leucogastroides folder
echo Renaming files in Lonchura leucogastroides...
cd "Dataset\Lonchura leucogastroides"
for %%f in (ebird_screenshot_*.png) do (
    set "filename=%%f"
    set "newname=!filename:ebird_screenshot_=lonchura_leucogastroides_!"
    ren "%%f" "!newname!"
)
cd ..\..

REM Rename files in Lonchura maja folder
echo Renaming files in Lonchura maja...
cd "Dataset\Lonchura maja"
for %%f in (ebird_screenshot_*.png) do (
    set "filename=%%f"
    set "newname=!filename:ebird_screenshot_=lonchura_maja_!"
    ren "%%f" "!newname!"
)
cd ..\..

REM Rename files in Lonchura punctulata folder
echo Renaming files in Lonchura punctulata...
cd "Dataset\Lonchura punctulata"
for %%f in (ebird_screenshot_*.png) do (
    set "filename=%%f"
    set "newname=!filename:ebird_screenshot_=lonchura_punctulata_!"
    ren "%%f" "!newname!"
)
cd ..\..

REM Rename files in Passer montanus folder
echo Renaming files in Passer montanus...
cd "Dataset\Passer montanus"
for %%f in (ebird_screenshot_*.png) do (
    set "filename=%%f"
    set "newname=!filename:ebird_screenshot_=passer_montanus_!"
    ren "%%f" "!newname!"
)
cd ..\..

echo.
echo ========================================
echo RENAME SELESAI!
echo ========================================
pause
