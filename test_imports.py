#!/usr/bin/env python3
"""
Test script to verify all required imports work correctly
"""

print("Testing imports...")

try:
    import tensorflow as tf
    print(f"✓ TensorFlow {tf.__version__} imported successfully")
except ImportError as e:
    print(f"✗ TensorFlow import failed: {e}")

try:
    from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
    print("✓ Keras layers imported successfully")
except ImportError as e:
    print(f"✗ Keras layers import failed: {e}")

try:
    from tensorflow.keras.optimizers import Adam
    print("✓ Keras optimizers imported successfully")
except ImportError as e:
    print(f"✗ Keras optimizers import failed: {e}")

try:
    from tensorflow.keras.applications import MobileNetV2
    print("✓ MobileNetV2 imported successfully")
except ImportError as e:
    print(f"✗ MobileNetV2 import failed: {e}")

try:
    from tensorflow.keras.models import Model
    print("✓ Keras Model imported successfully")
except ImportError as e:
    print(f"✗ Keras Model import failed: {e}")

try:
    from tensorflow.keras.preprocessing.image import ImageDataGenerator
    print("✓ ImageDataGenerator imported successfully")
except ImportError as e:
    print(f"✗ ImageDataGenerator import failed: {e}")

try:
    from sklearn.utils.class_weight import compute_class_weight
    print("✓ sklearn class_weight imported successfully")
except ImportError as e:
    print(f"✗ sklearn class_weight import failed: {e}")

try:
    import numpy as np
    print(f"✓ NumPy {np.__version__} imported successfully")
except ImportError as e:
    print(f"✗ NumPy import failed: {e}")

try:
    import matplotlib.pyplot as plt
    print("✓ Matplotlib imported successfully")
except ImportError as e:
    print(f"✗ Matplotlib import failed: {e}")

try:
    import seaborn as sns
    print("✓ Seaborn imported successfully")
except ImportError as e:
    print(f"✗ Seaborn import failed: {e}")

try:
    from sklearn.metrics import confusion_matrix
    print("✓ sklearn confusion_matrix imported successfully")
except ImportError as e:
    print(f"✗ sklearn confusion_matrix import failed: {e}")

print("\nAll imports tested!")

# Test basic TensorFlow functionality
print("\nTesting basic TensorFlow functionality...")
try:
    # Create a simple tensor
    x = tf.constant([1, 2, 3, 4])
    y = tf.constant([2, 3, 4, 5])
    z = tf.add(x, y)
    print(f"✓ Basic tensor operations work: {z.numpy()}")
    
    # Test GPU availability
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        print(f"✓ GPU available: {len(gpus)} GPU(s) detected")
    else:
        print("ℹ No GPU detected, using CPU")
        
except Exception as e:
    print(f"✗ TensorFlow functionality test failed: {e}")

print("\nTest completed!")
