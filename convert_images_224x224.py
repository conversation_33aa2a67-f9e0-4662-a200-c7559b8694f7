#!/usr/bin/env python3
"""
Script untuk mengkonversi gambar ke ukuran 224x224 untuk MobileNetV2
Mendukung dua metode: padding (mempertahankan aspect ratio) dan stretch
"""

import os
import cv2
import numpy as np
from PIL import Image
import argparse
from pathlib import Path

def resize_with_padding(image_path, output_path, target_size=(224, 224)):
    """
    Resize gambar dengan padding untuk mempertahankan aspect ratio
    """
    try:
        # Baca gambar
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Tidak bisa membaca: {image_path}")
            return False
        
        # Convert BGR ke RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert ke PIL Image
        pil_image = Image.fromarray(image)
        
        # Get original dimensions
        original_width, original_height = pil_image.size
        target_width, target_height = target_size
        
        # Calculate scaling factor
        scale = min(target_width / original_width, target_height / original_height)
        
        # Calculate new dimensions
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        
        # Resize image
        resized_image = pil_image.resize((new_width, new_height), Image.LANCZOS)
        
        # Create new image with target size and black padding
        new_image = Image.new('RGB', target_size, (0, 0, 0))
        
        # Calculate position to center the resized image
        paste_x = (target_width - new_width) // 2
        paste_y = (target_height - new_height) // 2
        
        # Paste resized image onto padded background
        new_image.paste(resized_image, (paste_x, paste_y))
        
        # Save image
        new_image.save(output_path, 'JPEG', quality=95)
        return True
        
    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        return False

def resize_with_stretch(image_path, output_path, target_size=(224, 224)):
    """
    Resize gambar dengan stretching (tanpa mempertahankan aspect ratio)
    """
    try:
        # Baca gambar
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Tidak bisa membaca: {image_path}")
            return False
        
        # Resize langsung ke target size
        resized_image = cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)
        
        # Save image
        cv2.imwrite(output_path, resized_image)
        return True
        
    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        return False

def convert_dataset(input_dir, output_dir, method='padding', target_size=(224, 224)):
    """
    Konversi seluruh dataset ke ukuran target
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directory
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Supported image extensions
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    total_processed = 0
    total_success = 0
    class_stats = {}
    
    print(f"🔄 Memulai konversi gambar ke {target_size[0]}x{target_size[1]}...")
    print(f"📁 Input: {input_dir}")
    print(f"📁 Output: {output_dir}")
    print(f"⚙️  Method: {method}")
    print("=" * 60)
    
    # Process each class directory
    for class_dir in input_path.iterdir():
        if class_dir.is_dir():
            class_name = class_dir.name
            print(f"\n📂 Processing: {class_name}")
            
            # Create output class directory
            output_class_dir = output_path / class_name
            output_class_dir.mkdir(exist_ok=True)
            
            # Get all image files
            image_files = [f for f in class_dir.iterdir() 
                          if f.suffix.lower() in image_extensions]
            
            class_processed = 0
            class_success = 0
            
            for i, image_file in enumerate(image_files):
                # Create output filename
                output_file = output_class_dir / f"{image_file.stem}.jpg"
                
                # Process image
                total_processed += 1
                class_processed += 1
                
                if method == 'padding':
                    success = resize_with_padding(str(image_file), str(output_file), target_size)
                else:  # stretch
                    success = resize_with_stretch(str(image_file), str(output_file), target_size)
                
                if success:
                    total_success += 1
                    class_success += 1
                
                # Progress indicator
                if (i + 1) % 10 == 0 or (i + 1) == len(image_files):
                    print(f"  Progress: {i + 1}/{len(image_files)} images")
            
            class_stats[class_name] = {
                'processed': class_processed,
                'success': class_success,
                'failed': class_processed - class_success
            }
            
            print(f"  ✅ {class_name}: {class_success}/{class_processed} berhasil")
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY KONVERSI")
    print("=" * 60)
    print(f"📈 Total images processed: {total_processed}")
    print(f"✅ Successfully converted: {total_success}")
    print(f"❌ Failed: {total_processed - total_success}")
    print(f"📊 Success rate: {(total_success/total_processed*100):.1f}%")
    
    print(f"\n📋 Detail per class:")
    for class_name, stats in class_stats.items():
        print(f"  📂 {class_name}:")
        print(f"     ✅ Success: {stats['success']}")
        print(f"     ❌ Failed: {stats['failed']}")
    
    print(f"\n🎯 Converted images saved to: {output_dir}")
    print("🚀 Ready for MobileNetV2 training!")

def main():
    parser = argparse.ArgumentParser(description='Convert images to 224x224 for MobileNetV2')
    parser.add_argument('--input', '-i', required=True, help='Input directory containing class subdirectories')
    parser.add_argument('--output', '-o', required=True, help='Output directory for converted images')
    parser.add_argument('--method', '-m', choices=['padding', 'stretch'], default='padding',
                       help='Conversion method: padding (preserve aspect ratio) or stretch')
    parser.add_argument('--size', '-s', type=int, nargs=2, default=[224, 224],
                       help='Target size (width height), default: 224 224')
    
    args = parser.parse_args()
    
    # Validate input directory
    if not os.path.exists(args.input):
        print(f"❌ Input directory tidak ditemukan: {args.input}")
        print("\n📋 Struktur direktori yang diharapkan:")
        print("input_directory/")
        print("  ├── class1/")
        print("  │   ├── image1.jpg")
        print("  │   └── image2.jpg")
        print("  ├── class2/")
        print("  │   ├── image3.jpg")
        print("  │   └── image4.jpg")
        print("  └── ...")
        return
    
    # Check if input directory has subdirectories
    input_path = Path(args.input)
    class_dirs = [d for d in input_path.iterdir() if d.is_dir()]
    
    if not class_dirs:
        print(f"❌ Tidak ada subdirectory class ditemukan di: {args.input}")
        return
    
    print(f"✅ Ditemukan {len(class_dirs)} class directories:")
    for class_dir in class_dirs:
        image_count = len([f for f in class_dir.iterdir() 
                          if f.suffix.lower() in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}])
        print(f"  📂 {class_dir.name}: {image_count} images")
    
    # Confirm before processing
    print(f"\n🎯 Target size: {args.size[0]}x{args.size[1]}")
    print(f"⚙️  Method: {args.method}")
    
    response = input("\n❓ Lanjutkan konversi? (y/n): ").lower().strip()
    if response != 'y':
        print("❌ Konversi dibatalkan.")
        return
    
    # Start conversion
    convert_dataset(args.input, args.output, args.method, tuple(args.size))

if __name__ == "__main__":
    # Jika dijalankan tanpa arguments, gunakan default paths
    import sys
    
    if len(sys.argv) == 1:
        print("🔧 Running with default settings...")
        print("📁 Input: ./data")
        print("📁 Output: ./data_224x224")
        print("⚙️  Method: padding")
        
        input_dir = "./Dataset"
        output_dir = "./ResizedDataset"
        
        if os.path.exists(input_dir):
            convert_dataset(input_dir, output_dir, method='padding')
        else:
            print(f"❌ Default input directory tidak ditemukan: {input_dir}")
            print("\n📋 Cara penggunaan:")
            print("python convert_images_224x224.py --input /path/to/input --output /path/to/output")
            print("\nAtau buat direktori 'data' dengan struktur:")
            print("data/")
            print("  ├── class1/")
            print("  ├── class2/")
            print("  └── ...")
    else:
        main()
