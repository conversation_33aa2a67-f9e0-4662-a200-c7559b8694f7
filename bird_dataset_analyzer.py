#!/usr/bin/env python3
"""
Bird Dataset Analyzer
Analisis khusus untuk dataset burung dengan visualisasi lengkap
"""

import os
import random
from collections import Counter
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
from tqdm import tqdm

def analyze_bird_dataset(data_dir):
    """
    Ana<PERSON>is komprehensif dataset burung
    """
    print("🐦 Analyzing bird dataset...")
    print(f"📁 Directory: {data_dir}")
    
    if not os.path.exists(data_dir):
        print(f"❌ Directory not found: {data_dir}")
        return None
    
    # Initialize analysis results
    analysis = {
        'classes': [],
        'total_images': 0,
        'class_distribution': {},
        'image_properties': {
            'sizes': [],
            'formats': Counter(),
            'aspect_ratios': [],
            'file_sizes': []
        },
        'quality_issues': {
            'corrupted': [],
            'too_small': [],
            'unusual_aspect': []
        }
    }
    
    # Analyze each bird class
    for class_name in os.listdir(data_dir):
        class_path = os.path.join(data_dir, class_name)
        
        if os.path.isdir(class_path):
            print(f"🔍 Analyzing class: {class_name}")
            
            # Get image files
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend([f for f in os.listdir(class_path) 
                                  if f.lower().endswith(ext)])
            
            if image_files:
                analysis['classes'].append(class_name)
                analysis['class_distribution'][class_name] = len(image_files)
                analysis['total_images'] += len(image_files)
                
                # Analyze image properties
                for img_file in tqdm(image_files, desc=f"Processing {class_name}"):
                    img_path = os.path.join(class_path, img_file)
                    
                    try:
                        # File size
                        file_size = os.path.getsize(img_path) / 1024  # KB
                        analysis['image_properties']['file_sizes'].append(file_size)
                        
                        # Image properties
                        with Image.open(img_path) as img:
                            width, height = img.size
                            analysis['image_properties']['sizes'].append((width, height))
                            analysis['image_properties']['formats'][img.format] += 1
                            
                            # Aspect ratio
                            aspect_ratio = width / height
                            analysis['image_properties']['aspect_ratios'].append(aspect_ratio)
                            
                            # Quality checks
                            if width < 100 or height < 100:
                                analysis['quality_issues']['too_small'].append(img_path)
                            
                            if aspect_ratio < 0.5 or aspect_ratio > 2.0:
                                analysis['quality_issues']['unusual_aspect'].append(img_path)
                                
                    except Exception as e:
                        analysis['quality_issues']['corrupted'].append((img_path, str(e)))
    
    return analysis

def create_comprehensive_report(analysis, output_dir='./analysis_results'):
    """
    Buat laporan analisis komprehensif
    """
    os.makedirs(output_dir, exist_ok=True)
    
    print("📊 Creating comprehensive analysis report...")
    
    # 1. Dataset Overview
    print(f"\n🐦 BIRD DATASET ANALYSIS REPORT")
    print(f"="*60)
    print(f"📁 Total classes: {len(analysis['classes'])}")
    print(f"📊 Total images: {analysis['total_images']}")
    print(f"🐦 Bird species: {', '.join(analysis['classes'])}")
    
    # 2. Class Distribution Analysis
    print(f"\n📈 CLASS DISTRIBUTION:")
    class_counts = list(analysis['class_distribution'].values())
    for class_name, count in analysis['class_distribution'].items():
        percentage = (count / analysis['total_images']) * 100
        print(f"   {class_name}: {count} images ({percentage:.1f}%)")
    
    # Balance analysis
    if class_counts:
        balance_ratio = min(class_counts) / max(class_counts)
        std_dev = np.std(class_counts)
        print(f"\n⚖️ BALANCE ANALYSIS:")
        print(f"   Balance ratio: {balance_ratio:.3f}")
        print(f"   Standard deviation: {std_dev:.1f}")
        
        if balance_ratio < 0.7:
            print(f"   ⚠️ Dataset is imbalanced! Consider data augmentation for minority classes.")
        else:
            print(f"   ✅ Dataset is well balanced.")
    
    # 3. Image Properties Analysis
    if analysis['image_properties']['sizes']:
        sizes_df = pd.DataFrame(analysis['image_properties']['sizes'], columns=['width', 'height'])
        
        print(f"\n📐 IMAGE SIZE ANALYSIS:")
        print(f"   Average size: {sizes_df['width'].mean():.0f} x {sizes_df['height'].mean():.0f}")
        print(f"   Size range: {sizes_df['width'].min()}-{sizes_df['width'].max()} x {sizes_df['height'].min()}-{sizes_df['height'].max()}")
        print(f"   Size std dev: {sizes_df['width'].std():.1f} x {sizes_df['height'].std():.1f}")
        
        # Aspect ratio analysis
        aspect_ratios = analysis['image_properties']['aspect_ratios']
        print(f"\n📏 ASPECT RATIO ANALYSIS:")
        print(f"   Average aspect ratio: {np.mean(aspect_ratios):.2f}")
        print(f"   Aspect ratio range: {min(aspect_ratios):.2f} - {max(aspect_ratios):.2f}")
        
        # File size analysis
        file_sizes = analysis['image_properties']['file_sizes']
        print(f"\n💾 FILE SIZE ANALYSIS:")
        print(f"   Average file size: {np.mean(file_sizes):.1f} KB")
        print(f"   File size range: {min(file_sizes):.1f} - {max(file_sizes):.1f} KB")
    
    # 4. Format Analysis
    print(f"\n🖼️ IMAGE FORMATS:")
    for format_name, count in analysis['image_properties']['formats'].items():
        percentage = (count / analysis['total_images']) * 100
        print(f"   {format_name}: {count} images ({percentage:.1f}%)")
    
    # 5. Quality Issues
    print(f"\n🔍 QUALITY ISSUES:")
    print(f"   Corrupted images: {len(analysis['quality_issues']['corrupted'])}")
    print(f"   Too small images: {len(analysis['quality_issues']['too_small'])}")
    print(f"   Unusual aspect ratios: {len(analysis['quality_issues']['unusual_aspect'])}")
    
    if analysis['quality_issues']['corrupted']:
        print(f"\n❌ CORRUPTED IMAGES:")
        for img_path, error in analysis['quality_issues']['corrupted'][:5]:
            print(f"   {img_path}: {error}")
        if len(analysis['quality_issues']['corrupted']) > 5:
            print(f"   ... and {len(analysis['quality_issues']['corrupted']) - 5} more")
    
    # 6. Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    # Size recommendations
    if sizes_df['width'].std() > 100 or sizes_df['height'].std() > 100:
        print(f"   📐 High size variation detected. Recommend resizing to 224x224 for CNN.")
    
    # Balance recommendations
    if balance_ratio < 0.7:
        minority_classes = [k for k, v in analysis['class_distribution'].items() 
                          if v < np.mean(class_counts)]
        print(f"   ⚖️ Apply data augmentation to: {', '.join(minority_classes)}")
    
    # Quality recommendations
    if len(analysis['quality_issues']['corrupted']) > 0:
        print(f"   🔧 Remove or fix {len(analysis['quality_issues']['corrupted'])} corrupted images")
    
    if len(analysis['quality_issues']['too_small']) > 0:
        print(f"   📏 Consider removing {len(analysis['quality_issues']['too_small'])} very small images")
    
    return analysis

def create_visualizations(analysis, output_dir='./analysis_results'):
    """
    Buat visualisasi analisis dataset
    """
    print("📊 Creating analysis visualizations...")
    
    # 1. Class Distribution
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Bar plot
    classes = list(analysis['class_distribution'].keys())
    counts = list(analysis['class_distribution'].values())
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3']
    
    axes[0, 0].bar(classes, counts, color=colors[:len(classes)])
    axes[0, 0].set_title('📊 Class Distribution', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('Bird Species')
    axes[0, 0].set_ylabel('Number of Images')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Add count labels
    for i, count in enumerate(counts):
        axes[0, 0].text(i, count + max(counts)*0.01, str(count), 
                       ha='center', va='bottom', fontweight='bold')
    
    # Pie chart
    axes[0, 1].pie(counts, labels=classes, autopct='%1.1f%%', startangle=90,
                   colors=colors[:len(classes)])
    axes[0, 1].set_title('🥧 Class Proportion', fontsize=14, fontweight='bold')
    
    # Image size distribution
    if analysis['image_properties']['sizes']:
        sizes_df = pd.DataFrame(analysis['image_properties']['sizes'], columns=['width', 'height'])
        
        axes[1, 0].scatter(sizes_df['width'], sizes_df['height'], alpha=0.6, color='#45B7D1')
        axes[1, 0].set_title('📐 Image Size Distribution', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('Width (pixels)')
        axes[1, 0].set_ylabel('Height (pixels)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Add reference lines for common sizes
        axes[1, 0].axhline(y=224, color='red', linestyle='--', alpha=0.7, label='224px')
        axes[1, 0].axvline(x=224, color='red', linestyle='--', alpha=0.7)
        axes[1, 0].legend()
    
    # Aspect ratio histogram
    if analysis['image_properties']['aspect_ratios']:
        axes[1, 1].hist(analysis['image_properties']['aspect_ratios'], bins=30, 
                       color='#96CEB4', alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('📏 Aspect Ratio Distribution', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('Aspect Ratio (width/height)')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].axvline(x=1.0, color='red', linestyle='--', alpha=0.7, label='Square (1:1)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save visualization
    viz_path = os.path.join(output_dir, 'dataset_analysis.png')
    plt.savefig(viz_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Analysis visualization saved to: {viz_path}")

def create_sample_showcase(data_dir, analysis, output_dir='./analysis_results', samples_per_class=6):
    """
    Buat showcase sample gambar dari setiap kelas burung
    """
    print("🖼️ Creating bird species showcase...")
    
    classes = analysis['classes']
    
    fig, axes = plt.subplots(len(classes), samples_per_class, 
                            figsize=(samples_per_class * 2.5, len(classes) * 2.5))
    
    if len(classes) == 1:
        axes = axes.reshape(1, -1)
    
    for i, class_name in enumerate(classes):
        class_path = os.path.join(data_dir, class_name)
        image_files = [f for f in os.listdir(class_path) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
        
        # Sample random images
        sample_files = random.sample(image_files, min(samples_per_class, len(image_files)))
        
        for j, img_file in enumerate(sample_files):
            img_path = os.path.join(class_path, img_file)
            try:
                img = Image.open(img_path)
                axes[i, j].imshow(img)
                
                # Add image info
                width, height = img.size
                file_size = os.path.getsize(img_path) / 1024  # KB
                
                title = f'{width}x{height}\n{file_size:.0f}KB'
                axes[i, j].set_title(title, fontsize=8)
                axes[i, j].axis('off')
                
            except Exception as e:
                axes[i, j].text(0.5, 0.5, f'Error\n{str(e)[:15]}...', 
                               ha='center', va='center', transform=axes[i, j].transAxes,
                               fontsize=8, color='red')
                axes[i, j].axis('off')
        
        # Add class label
        if samples_per_class > 0:
            axes[i, 0].text(-0.1, 0.5, class_name, rotation=90, 
                           ha='center', va='center', transform=axes[i, 0].transAxes,
                           fontsize=12, fontweight='bold')
        
        # Hide unused subplots
        for j in range(len(sample_files), samples_per_class):
            axes[i, j].axis('off')
    
    plt.suptitle('🐦 Bird Species Showcase', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Save showcase
    showcase_path = os.path.join(output_dir, 'bird_species_showcase.png')
    plt.savefig(showcase_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"🖼️ Bird species showcase saved to: {showcase_path}")

def main():
    """
    Main function untuk analisis dataset burung
    """
    print("🐦 Bird Dataset Analyzer")
    print("="*50)
    
    # Configuration
    data_dir = './Dataset'  # Sesuaikan dengan path dataset Anda
    output_dir = './analysis_results'
    
    # Check if dataset exists
    if not os.path.exists(data_dir):
        print(f"❌ Dataset directory not found: {data_dir}")
        print("Please update the data_dir variable with your dataset path.")
        
        # Try alternative paths
        alternatives = ['./ResizedDataset', './SplitDataset/train', './data']
        for alt_path in alternatives:
            if os.path.exists(alt_path):
                print(f"✅ Found alternative dataset: {alt_path}")
                data_dir = alt_path
                break
        else:
            return
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. Analyze dataset
        analysis = analyze_bird_dataset(data_dir)
        
        if analysis and analysis['total_images'] > 0:
            # 2. Create comprehensive report
            create_comprehensive_report(analysis, output_dir)
            
            # 3. Create visualizations
            create_visualizations(analysis, output_dir)
            
            # 4. Create sample showcase
            create_sample_showcase(data_dir, analysis, output_dir)
            
            # 5. Save analysis results
            import json
            analysis_copy = analysis.copy()
            # Convert non-serializable objects
            analysis_copy['image_properties']['formats'] = dict(analysis_copy['image_properties']['formats'])
            
            with open(os.path.join(output_dir, 'analysis_results.json'), 'w') as f:
                json.dump(analysis_copy, f, indent=2, default=str)
            
            print(f"\n🎉 Analysis completed successfully!")
            print(f"📁 Results saved to: {output_dir}")
            print(f"📊 Total images analyzed: {analysis['total_images']}")
            print(f"🐦 Bird species found: {len(analysis['classes'])}")
            
        else:
            print("❌ No valid dataset found or dataset is empty")
            
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        print("Please check your dataset path and structure.")

if __name__ == "__main__":
    main()
