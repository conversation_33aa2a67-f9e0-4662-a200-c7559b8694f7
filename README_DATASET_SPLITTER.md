# Dataset Splitter - Pembagi Dataset untuk Machine Learning

Script Python untuk membagi dataset menjadi set training dan validation dengan mendukung dua jenis struktur:
1. **Single Folder**: Semua gambar dalam satu folder
2. **Multi-Class**: Struktur folder dengan subfolder untuk setiap kelas

## 🚀 Fitur Utama

- ✅ **Auto-detection**: Mendeteksi struktur dataset secara otomatis
- ✅ **Multi-class support**: Mempertahankan struktur kelas saat pembagian
- ✅ **Stratified splitting**: Pembagian proporsional untuk setiap kelas
- ✅ **Preview mode**: Lihat hasil sebelum menyalin file
- ✅ **Reproducible**: Menggunakan random seed untuk hasil konsisten
- ✅ **Analysis tools**: Analisis dataset yang sudah dibagi

## 📁 Struktur Input dan Output

### Input (ResizedDataset/Dataset/):
```
ResizedDataset/Dataset/
├── Lonchura leucogastroides/
│   ├── Lonchura leucogastroides 1.jpg
│   ├── Lonchura leucogastroides 2.jpg
│   └── ... (500+ files)
├── Lonchura maja/
│   ├── Lonchura maja 1.jpg
│   └── ... (400+ files)
├── Lonchura punctulata/
├── Passer montanus/
└── Unknown/
    ├── Anisolabididae/
    ├── Carabidae/
    ├── Coccinellidae/
    └── ... (9 subfolder)
```

### Output (SplitDataset/):
```
SplitDataset/
├── train/
│   ├── Lonchura leucogastroides/     (70% files)
│   ├── Lonchura maja/                (70% files)
│   ├── Lonchura punctulata/          (70% files)
│   ├── Passer montanus/              (70% files)
│   ├── Anisolabididae/               (70% files)
│   ├── Carabidae/                    (70% files)
│   └── ... (semua kelas)
└── validation/
    ├── Lonchura leucogastroides/     (30% files)
    ├── Lonchura maja/                (30% files)
    └── ... (semua kelas)
```

## 🚀 Cara Penggunaan

### Metode 1: Mode Interaktif (Recommended)

```bash
python dataset_splitter.py
```

Script akan meminta input:
```
📁 Source directory (default: ResizedDataset/Dataset): [Enter]
📁 Output directory (default: SplitDataset): [Enter]
📊 Train ratio (default: 0.7 = 70%): [Enter]
🎲 Random seed (default: 42): [Enter]
```

### Metode 2: Quick Split (Parameter Default)

```bash
python dataset_splitter.py quick
```

Langsung menggunakan:
- Source: `ResizedDataset/Dataset`
- Output: `SplitDataset`
- Ratio: 70:30
- Seed: 42

### Metode 3: Analisis Dataset

```bash
# Analisis dataset yang sudah dibagi
python dataset_splitter.py analyze SplitDataset

# Atau dengan path default
python dataset_splitter.py analyze
```

## 📊 Contoh Output

### Preview Mode:
```
🔄 DATASET SPLITTER
============================================================
📁 Source: ResizedDataset/Dataset
📁 Output: SplitDataset
📊 Train ratio: 70%
📊 Validation ratio: 30%
🎲 Random seed: 42
🔍 MODE: PREVIEW

📋 Ditemukan 13 kelas:
  📁 Anisolabididae: 650 files → Train: 455, Val: 195
  📁 Carabidae: 650 files → Train: 455, Val: 195
  📁 Coccinellidae: 650 files → Train: 455, Val: 195
  📁 Hirundo rustica: 200 files → Train: 140, Val: 60
  📁 Ictinaetus malaiensis: 200 files → Train: 140, Val: 60
  📁 Lonchura leucogastroides: 500 files → Train: 350, Val: 150
  📁 Lonchura maja: 400 files → Train: 280, Val: 120
  📁 Lonchura punctulata: 300 files → Train: 210, Val: 90
  📁 Paddy: 650 files → Train: 455, Val: 195
  📁 Passer montanus: 200 files → Train: 140, Val: 60
  📁 Pycnonotus aurigaster: 200 files → Train: 140, Val: 60
  📁 Staphylinidae: 650 files → Train: 455, Val: 195
  📁 Todiramphus chloris: 200 files → Train: 140, Val: 60

📊 Total keseluruhan: 5850 files
📊 Total Train: 4095 files (70.0%)
📊 Total Validation: 1755 files (30.0%)
```

### Analisis Dataset:
```
📊 ANALISIS DATASET: SplitDataset
==================================================
📚 TRAINING SET: 4095 files
📝 VALIDATION SET: 1755 files
📊 Total: 5850 files
📊 Ratio: 70.0% train, 30.0% val

📋 Detail per kelas:
  📁 Anisolabididae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Carabidae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  📁 Coccinellidae: 650 total → Train: 455 (70.0%), Val: 195 (30.0%)
  ... (dan seterusnya)
```

## ⚙️ Parameter dan Opsi

### Train Ratio
- **Default**: 0.7 (70% training, 30% validation)
- **Range**: 0.1 - 0.9
- **Contoh**: 0.8 = 80% training, 20% validation

### Random Seed
- **Default**: 42
- **Fungsi**: Memastikan hasil pembagian yang konsisten
- **Contoh**: Seed yang sama akan menghasilkan pembagian yang sama

### Source Directory
- **Default**: `ResizedDataset/Dataset`
- **Format**: Path relatif atau absolut
- **Requirement**: Harus berisi subfolder (kelas)

### Output Directory
- **Default**: `SplitDataset`
- **Struktur**: Otomatis membuat `train/` dan `validation/`
- **Overwrite**: Akan menimpa jika sudah ada

## 🛡️ Fitur Keamanan

1. **Preview Mode**: Selalu preview sebelum copy
2. **Validasi Input**: Cek keberadaan directory dan file
3. **Konfirmasi**: Konfirmasi sebelum melakukan operasi
4. **Error Handling**: Tangani error dengan baik
5. **Backup Metadata**: Preservasi timestamp dan permission

## 📈 Algoritma Pembagian

1. **Scan Kelas**: Deteksi semua subfolder sebagai kelas
2. **Count Files**: Hitung jumlah file per kelas
3. **Calculate Split**: Hitung pembagian berdasarkan ratio
4. **Shuffle**: Acak file dengan seed yang konsisten
5. **Split**: Bagi file sesuai perhitungan
6. **Copy**: Copy file ke direktori tujuan

## 💡 Tips Penggunaan

### Untuk Dataset Besar
```bash
# Gunakan preview dulu untuk memastikan
python dataset_splitter.py
# Pilih preview → konfirmasi → copy
```

### Untuk Eksperimen
```bash
# Gunakan seed berbeda untuk variasi
python dataset_splitter.py
# Input seed: 123, 456, 789, dst.
```

### Untuk Ratio Custom
```bash
# 80:20 split
python dataset_splitter.py
# Input ratio: 0.8
```

### Untuk Analisis
```bash
# Cek hasil pembagian
python dataset_splitter.py analyze
```

## 🔧 Troubleshooting

### Error: "Source directory tidak ditemukan"
- Pastikan path `ResizedDataset/Dataset` benar
- Cek apakah Anda berada di direktori yang tepat
- Gunakan path absolut jika perlu

### Error: "Tidak ada subfolder"
- Pastikan struktur dataset benar
- Setiap kelas harus dalam subfolder terpisah
- Cek apakah ada file di root directory

### Error: "Permission denied"
- Jalankan sebagai administrator jika perlu
- Pastikan direktori output dapat ditulis
- Cek permission file sumber

### Hasil tidak konsisten
- Pastikan menggunakan seed yang sama
- Jangan mengubah urutan file di source
- Gunakan Python version yang sama

## 📊 Statistik Contoh

Berdasarkan dataset Anda:

| Kelas | Total Files | Train (70%) | Val (30%) |
|-------|-------------|-------------|-----------|
| Lonchura leucogastroides | 500 | 350 | 150 |
| Lonchura maja | 400 | 280 | 120 |
| Lonchura punctulata | 300 | 210 | 90 |
| Passer montanus | 200 | 140 | 60 |
| Anisolabididae | 650 | 455 | 195 |
| Carabidae | 650 | 455 | 195 |
| Coccinellidae | 650 | 455 | 195 |
| Hirundo rustica | 200 | 140 | 60 |
| Ictinaetus malaiensis | 200 | 140 | 60 |
| Paddy | 650 | 455 | 195 |
| Pycnonotus aurigaster | 200 | 140 | 60 |
| Staphylinidae | 650 | 455 | 195 |
| Todiramphus chloris | 200 | 140 | 60 |
| **TOTAL** | **5850** | **4095** | **1755** |

## 🎯 Use Cases

### Machine Learning Training
```python
# Setelah split, gunakan untuk training
train_dir = "SplitDataset/train"
val_dir = "SplitDataset/validation"

# TensorFlow/Keras
train_ds = tf.keras.preprocessing.image_dataset_from_directory(train_dir)
val_ds = tf.keras.preprocessing.image_dataset_from_directory(val_dir)
```

### Data Augmentation
```python
# Augmentasi hanya pada training set
from tensorflow.keras.preprocessing.image import ImageDataGenerator

train_datagen = ImageDataGenerator(
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    horizontal_flip=True
)

# Validation tanpa augmentasi
val_datagen = ImageDataGenerator()
```

### Cross Validation
```bash
# Buat multiple split dengan seed berbeda
python dataset_splitter.py  # seed: 42
python dataset_splitter.py  # seed: 123
python dataset_splitter.py  # seed: 456
```

## 📝 Catatan

- Script mempertahankan struktur folder asli
- File disalin, bukan dipindah (source tetap utuh)
- Mendukung semua format gambar umum
- Pembagian berdasarkan jumlah file, bukan ukuran
- Seed memastikan reproducibility untuk penelitian
