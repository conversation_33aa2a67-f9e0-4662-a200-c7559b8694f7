#!/usr/bin/env python3
"""
Script untuk setup dan konfigurasi data lokal
Mengubah konfigurasi notebook untuk menggunakan data yang sudah ada
"""

import os
import json
from pathlib import Path

def detect_available_datasets():
    """
    Deteksi dataset yang tersedia di direktori lokal
    """
    print("🔍 Mendeteksi dataset yang tersedia...")
    
    datasets = {}
    
    # Cek berbagai direktori data
    data_directories = [
        ('ResizedDataset', 'Data yang sudah diproses ke 224x224'),
        ('Dataset', 'Data asli dengan berbagai ukuran'),
        ('data', 'Direktori data alternatif'),
        ('processed_data', 'Data hasil preprocessing')
    ]
    
    for dir_name, description in data_directories:
        if os.path.exists(dir_name):
            # Hitung jumlah kelas dan gambar
            classes = []
            total_images = 0
            
            for item in os.listdir(dir_name):
                item_path = os.path.join(dir_name, item)
                if os.path.isdir(item_path):
                    # Hitung gambar di kelas ini
                    image_files = [f for f in os.listdir(item_path) 
                                 if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                    if image_files:  # Hanya tambahkan jika ada gambar
                        classes.append({
                            'name': item,
                            'count': len(image_files)
                        })
                        total_images += len(image_files)
            
            if classes:  # Hanya tambahkan jika ada kelas dengan gambar
                datasets[dir_name] = {
                    'description': description,
                    'classes': classes,
                    'total_images': total_images,
                    'num_classes': len(classes)
                }
    
    return datasets

def display_dataset_info(datasets):
    """
    Tampilkan informasi dataset yang tersedia
    """
    if not datasets:
        print("❌ Tidak ditemukan dataset yang valid!")
        print("\n📋 Untuk menggunakan script ini, pastikan Anda memiliki salah satu direktori berikut:")
        print("  - ResizedDataset/ (recommended - data sudah 224x224)")
        print("  - Dataset/ (data asli)")
        print("  - data/ (direktori alternatif)")
        print("\nSetiap direktori harus berisi subdirektori untuk setiap kelas burung.")
        return None
    
    print(f"\n📊 Dataset yang tersedia:")
    print("-" * 60)
    
    for i, (dir_name, info) in enumerate(datasets.items(), 1):
        print(f"{i}. {dir_name}")
        print(f"   📝 {info['description']}")
        print(f"   📂 {info['num_classes']} kelas, {info['total_images']} gambar total")
        print(f"   📋 Kelas:")
        for cls in info['classes']:
            print(f"      - {cls['name']}: {cls['count']} gambar")
        print()
    
    return datasets

def select_dataset(datasets):
    """
    Pilih dataset yang akan digunakan
    """
    if len(datasets) == 1:
        selected = list(datasets.keys())[0]
        print(f"✅ Menggunakan dataset: {selected}")
        return selected
    
    print("🔧 Pilih dataset yang ingin digunakan:")
    dataset_list = list(datasets.keys())
    
    for i, dataset in enumerate(dataset_list, 1):
        print(f"{i}. {dataset} - {datasets[dataset]['description']}")
    
    while True:
        try:
            choice = int(input("\nMasukkan pilihan (nomor): ")) - 1
            if 0 <= choice < len(dataset_list):
                selected = dataset_list[choice]
                print(f"✅ Dataset dipilih: {selected}")
                return selected
            else:
                print("❌ Pilihan tidak valid!")
        except ValueError:
            print("❌ Masukkan nomor yang valid!")

def create_config_file(selected_dataset, dataset_info):
    """
    Buat file konfigurasi untuk notebook
    """
    config = {
        'data_directory': selected_dataset,
        'num_classes': dataset_info['num_classes'],
        'total_images': dataset_info['total_images'],
        'classes': {cls['name']: cls['count'] for cls in dataset_info['classes']},
        'batch_size': 32,  # Sesuaikan untuk environment lokal
        'validation_split': 0.3,
        'target_size': [224, 224],
        'preprocessing_method': 'padding' if 'Resized' not in selected_dataset else 'none'
    }
    
    # Simpan konfigurasi
    with open('local_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Konfigurasi disimpan ke 'local_config.json'")
    return config

def generate_notebook_code(config):
    """
    Generate kode Python untuk digunakan di notebook
    """
    code = f'''# Konfigurasi untuk data lokal
# File ini di-generate otomatis oleh setup_local_data.py

import os
import json

# Load konfigurasi
with open('local_config.json', 'r') as f:
    config = json.load(f)

# Konfigurasi data
main_data_dir = config['data_directory']
num_classes = config['num_classes']
batch_size = config['batch_size']
validation_split = config['validation_split']
target_size = tuple(config['target_size'])

print(f"📁 Menggunakan data dari: {{main_data_dir}}")
print(f"📊 Jumlah kelas: {{num_classes}}")
print(f"📊 Total gambar: {{config['total_images']}}")
print(f"🔧 Batch size: {{batch_size}}")
print(f"📊 Validation split: {{validation_split}}")

# Tampilkan info kelas
print("\\n📋 Distribusi kelas:")
for class_name, count in config['classes'].items():
    print(f"  - {{class_name}}: {{count}} gambar")

# Cek apakah direktori data ada
if os.path.exists(main_data_dir):
    print(f"\\n✅ Data directory ditemukan: {{main_data_dir}}")
else:
    print(f"\\n❌ Data directory tidak ditemukan: {{main_data_dir}}")
    print("Silakan jalankan setup_local_data.py lagi untuk mengkonfigurasi ulang.")
'''
    
    # Simpan kode
    with open('local_data_config.py', 'w') as f:
        f.write(code)
    
    print(f"✅ Kode konfigurasi disimpan ke 'local_data_config.py'")

def create_simple_training_script(config):
    """
    Buat script training sederhana
    """
    script_content = f'''#!/usr/bin/env python3
"""
Script training sederhana menggunakan konfigurasi lokal
"""

# Import konfigurasi
exec(open('local_data_config.py').read())

# Import libraries
import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam

print("\\n🚀 Memulai setup model...")

# Data generators
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
    validation_split=validation_split
)

valid_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=validation_split
)

# Create generators
train_generator = train_datagen.flow_from_directory(
    main_data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=True,
    subset="training"
)

valid_generator = valid_datagen.flow_from_directory(
    main_data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=False,
    subset="validation"
)

print(f"✅ Data generators created!")
print(f"📊 Training samples: {{train_generator.samples}}")
print(f"📊 Validation samples: {{valid_generator.samples}}")

# Create model
print("\\n🏗️  Creating MobileNetV2 model...")

base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=(*target_size, 3))

# Freeze base model
for layer in base_model.layers:
    layer.trainable = False

# Add custom head
x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
x = Dense(1024, activation='relu')(x)
x = Dropout(0.5)(x)
predictions = Dense(num_classes, activation='softmax')(x)

model = Model(inputs=base_model.input, outputs=predictions)

# Compile
model.compile(
    optimizer=Adam(learning_rate=1e-4),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

print("✅ Model created and compiled!")
print(f"📊 Total parameters: {{model.count_params():,}}")

# Training
print("\\n🚀 Starting training...")

history = model.fit(
    train_generator,
    epochs=20,
    validation_data=valid_generator,
    verbose=1
)

# Save model
model.save('birds_classification_local.h5')
print("\\n✅ Model saved as 'birds_classification_local.h5'")

# Evaluate
test_loss, test_accuracy = model.evaluate(valid_generator)
print(f"\\n📈 Final Results:")
print(f"Test Loss: {{test_loss:.4f}}")
print(f"Test Accuracy: {{test_accuracy:.4f}}")
'''
    
    with open('simple_train.py', 'w') as f:
        f.write(script_content)
    
    print(f"✅ Script training sederhana disimpan ke 'simple_train.py'")

def main():
    """
    Main function
    """
    print("="*60)
    print("🔧 SETUP DATA LOKAL UNTUK MOBILENETV2")
    print("="*60)
    
    # Deteksi dataset
    datasets = detect_available_datasets()
    
    # Tampilkan info dataset
    datasets = display_dataset_info(datasets)
    if not datasets:
        return
    
    # Pilih dataset
    selected_dataset = select_dataset(datasets)
    dataset_info = datasets[selected_dataset]
    
    # Buat konfigurasi
    config = create_config_file(selected_dataset, dataset_info)
    
    # Generate kode notebook
    generate_notebook_code(config)
    
    # Buat script training sederhana
    create_simple_training_script(config)
    
    print("\n" + "="*60)
    print("✅ SETUP SELESAI!")
    print("="*60)
    print("\n📋 File yang dibuat:")
    print("  - local_config.json (konfigurasi)")
    print("  - local_data_config.py (kode untuk notebook)")
    print("  - simple_train.py (script training sederhana)")
    print("\n🚀 Cara menggunakan:")
    print("  1. Jalankan 'python simple_train.py' untuk training langsung")
    print("  2. Atau import 'local_data_config.py' di notebook Anda")
    print("  3. Atau gunakan 'local_training_script.py' untuk training lengkap")

if __name__ == "__main__":
    main()
