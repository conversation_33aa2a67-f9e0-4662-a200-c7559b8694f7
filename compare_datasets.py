#!/usr/bin/env python3
"""
Compare Original vs Resized Dataset
Script untuk membandingkan dataset asli dengan hasil resize
"""

import os
import glob
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np
from tqdm import tqdm
from collections import defaultdict, Counter
import pandas as pd

def analyze_dataset_structure(dataset_path, name="Dataset"):
    """
    Analisis struktur dan properti dataset
    """
    print(f"🔍 Analyzing {name}: {dataset_path}")
    
    if not os.path.exists(dataset_path):
        print(f"❌ {name} not found: {dataset_path}")
        return None
    
    analysis = {
        'name': name,
        'path': dataset_path,
        'total_images': 0,
        'classes': defaultdict(int),
        'sizes': [],
        'formats': Counter(),
        'file_sizes': [],  # in KB
        'aspect_ratios': []
    }
    
    # Get all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    all_images = []
    
    for ext in image_extensions:
        pattern = os.path.join(dataset_path, '**', ext)
        all_images.extend(glob.glob(pattern, recursive=True))
    
    analysis['total_images'] = len(all_images)
    
    if analysis['total_images'] == 0:
        print(f"⚠️ No images found in {name}")
        return analysis
    
    # Analyze each image
    for img_path in tqdm(all_images, desc=f"Analyzing {name}"):
        try:
            # Get class name
            rel_path = os.path.relpath(img_path, dataset_path)
            class_name = rel_path.split(os.sep)[0] if os.sep in rel_path else 'unknown'
            analysis['classes'][class_name] += 1
            
            # File size
            file_size = os.path.getsize(img_path) / 1024  # KB
            analysis['file_sizes'].append(file_size)
            
            # Image properties
            with Image.open(img_path) as img:
                width, height = img.size
                analysis['sizes'].append((width, height))
                analysis['formats'][img.format] += 1
                
                # Aspect ratio
                aspect_ratio = width / height if height > 0 else 0
                analysis['aspect_ratios'].append(aspect_ratio)
                
        except Exception as e:
            print(f"⚠️ Error analyzing {img_path}: {e}")
    
    return analysis

def create_comparison_report(original_analysis, resized_analysis, output_file='dataset_comparison.txt'):
    """
    Buat laporan perbandingan dataset
    """
    print("📋 Creating comparison report...")
    
    with open(output_file, 'w') as f:
        f.write("DATASET COMPARISON REPORT\n")
        f.write("="*50 + "\n\n")
        
        # Summary comparison
        f.write("SUMMARY COMPARISON:\n")
        f.write("-"*30 + "\n")
        f.write(f"{'Metric':<25} {'Original':<15} {'Resized':<15} {'Change':<15}\n")
        f.write("-"*70 + "\n")
        
        # Total images
        orig_total = original_analysis['total_images'] if original_analysis else 0
        resized_total = resized_analysis['total_images'] if resized_analysis else 0
        change = resized_total - orig_total
        f.write(f"{'Total Images':<25} {orig_total:<15} {resized_total:<15} {change:+d}\n")
        
        # Classes
        orig_classes = len(original_analysis['classes']) if original_analysis else 0
        resized_classes = len(resized_analysis['classes']) if resized_analysis else 0
        f.write(f"{'Number of Classes':<25} {orig_classes:<15} {resized_classes:<15} {resized_classes-orig_classes:+d}\n")
        
        # Average file size
        if original_analysis and original_analysis['file_sizes']:
            orig_avg_size = np.mean(original_analysis['file_sizes'])
        else:
            orig_avg_size = 0
            
        if resized_analysis and resized_analysis['file_sizes']:
            resized_avg_size = np.mean(resized_analysis['file_sizes'])
        else:
            resized_avg_size = 0
            
        f.write(f"{'Avg File Size (KB)':<25} {orig_avg_size:<15.1f} {resized_avg_size:<15.1f} {resized_avg_size-orig_avg_size:+.1f}\n")
        
        f.write("\n")
        
        # Class distribution comparison
        f.write("CLASS DISTRIBUTION COMPARISON:\n")
        f.write("-"*40 + "\n")
        f.write(f"{'Class':<25} {'Original':<15} {'Resized':<15} {'Difference':<15}\n")
        f.write("-"*70 + "\n")
        
        all_classes = set()
        if original_analysis:
            all_classes.update(original_analysis['classes'].keys())
        if resized_analysis:
            all_classes.update(resized_analysis['classes'].keys())
        
        for class_name in sorted(all_classes):
            orig_count = original_analysis['classes'].get(class_name, 0) if original_analysis else 0
            resized_count = resized_analysis['classes'].get(class_name, 0) if resized_analysis else 0
            diff = resized_count - orig_count
            f.write(f"{class_name:<25} {orig_count:<15} {resized_count:<15} {diff:+d}\n")
        
        f.write("\n")
        
        # Size analysis
        if original_analysis and original_analysis['sizes']:
            f.write("SIZE ANALYSIS:\n")
            f.write("-"*20 + "\n")
            
            orig_sizes = np.array(original_analysis['sizes'])
            f.write("Original Dataset:\n")
            f.write(f"  Average size: {orig_sizes[:, 0].mean():.0f} x {orig_sizes[:, 1].mean():.0f}\n")
            f.write(f"  Size range: {orig_sizes[:, 0].min()}-{orig_sizes[:, 0].max()} x {orig_sizes[:, 1].min()}-{orig_sizes[:, 1].max()}\n")
            f.write(f"  Size std dev: {orig_sizes[:, 0].std():.1f} x {orig_sizes[:, 1].std():.1f}\n")
            
        if resized_analysis and resized_analysis['sizes']:
            resized_sizes = np.array(resized_analysis['sizes'])
            f.write("\nResized Dataset:\n")
            f.write(f"  Average size: {resized_sizes[:, 0].mean():.0f} x {resized_sizes[:, 1].mean():.0f}\n")
            f.write(f"  Size range: {resized_sizes[:, 0].min()}-{resized_sizes[:, 0].max()} x {resized_sizes[:, 1].min()}-{resized_sizes[:, 1].max()}\n")
            f.write(f"  Size std dev: {resized_sizes[:, 0].std():.1f} x {resized_sizes[:, 1].std():.1f}\n")
        
        f.write("\n")
        
        # Format comparison
        f.write("FORMAT DISTRIBUTION:\n")
        f.write("-"*25 + "\n")
        
        all_formats = set()
        if original_analysis:
            all_formats.update(original_analysis['formats'].keys())
        if resized_analysis:
            all_formats.update(resized_analysis['formats'].keys())
        
        f.write(f"{'Format':<10} {'Original':<15} {'Resized':<15} {'Change':<15}\n")
        f.write("-"*55 + "\n")
        
        for format_name in sorted(all_formats):
            orig_count = original_analysis['formats'].get(format_name, 0) if original_analysis else 0
            resized_count = resized_analysis['formats'].get(format_name, 0) if resized_analysis else 0
            change = resized_count - orig_count
            f.write(f"{format_name:<10} {orig_count:<15} {resized_count:<15} {change:+d}\n")
    
    print(f"✅ Comparison report saved to: {output_file}")

def create_visual_comparison(original_analysis, resized_analysis, output_file='dataset_comparison.png'):
    """
    Buat visualisasi perbandingan dataset
    """
    print("📊 Creating visual comparison...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. Class distribution comparison
    if original_analysis and resized_analysis:
        classes = list(set(original_analysis['classes'].keys()) | set(resized_analysis['classes'].keys()))
        orig_counts = [original_analysis['classes'].get(c, 0) for c in classes]
        resized_counts = [resized_analysis['classes'].get(c, 0) for c in classes]
        
        x = np.arange(len(classes))
        width = 0.35
        
        axes[0, 0].bar(x - width/2, orig_counts, width, label='Original', alpha=0.8)
        axes[0, 0].bar(x + width/2, resized_counts, width, label='Resized', alpha=0.8)
        axes[0, 0].set_title('Class Distribution Comparison')
        axes[0, 0].set_xlabel('Classes')
        axes[0, 0].set_ylabel('Number of Images')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(classes, rotation=45, ha='right')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Size distribution (original)
    if original_analysis and original_analysis['sizes']:
        orig_sizes = np.array(original_analysis['sizes'])
        axes[0, 1].scatter(orig_sizes[:, 0], orig_sizes[:, 1], alpha=0.6, s=10)
        axes[0, 1].set_title('Original Dataset - Size Distribution')
        axes[0, 1].set_xlabel('Width (pixels)')
        axes[0, 1].set_ylabel('Height (pixels)')
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Size distribution (resized)
    if resized_analysis and resized_analysis['sizes']:
        resized_sizes = np.array(resized_analysis['sizes'])
        axes[0, 2].scatter(resized_sizes[:, 0], resized_sizes[:, 1], alpha=0.6, s=10, color='orange')
        axes[0, 2].set_title('Resized Dataset - Size Distribution')
        axes[0, 2].set_xlabel('Width (pixels)')
        axes[0, 2].set_ylabel('Height (pixels)')
        axes[0, 2].grid(True, alpha=0.3)
    
    # 4. File size comparison
    if original_analysis and resized_analysis:
        if original_analysis['file_sizes'] and resized_analysis['file_sizes']:
            axes[1, 0].hist(original_analysis['file_sizes'], bins=30, alpha=0.7, label='Original', density=True)
            axes[1, 0].hist(resized_analysis['file_sizes'], bins=30, alpha=0.7, label='Resized', density=True)
            axes[1, 0].set_title('File Size Distribution')
            axes[1, 0].set_xlabel('File Size (KB)')
            axes[1, 0].set_ylabel('Density')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
    
    # 5. Aspect ratio comparison
    if original_analysis and resized_analysis:
        if original_analysis['aspect_ratios'] and resized_analysis['aspect_ratios']:
            axes[1, 1].hist(original_analysis['aspect_ratios'], bins=30, alpha=0.7, label='Original', density=True)
            axes[1, 1].hist(resized_analysis['aspect_ratios'], bins=30, alpha=0.7, label='Resized', density=True)
            axes[1, 1].set_title('Aspect Ratio Distribution')
            axes[1, 1].set_xlabel('Aspect Ratio (width/height)')
            axes[1, 1].set_ylabel('Density')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
    
    # 6. Summary metrics
    metrics_text = "SUMMARY METRICS\n\n"
    
    if original_analysis:
        metrics_text += f"Original Dataset:\n"
        metrics_text += f"  Total Images: {original_analysis['total_images']}\n"
        metrics_text += f"  Classes: {len(original_analysis['classes'])}\n"
        if original_analysis['file_sizes']:
            metrics_text += f"  Avg File Size: {np.mean(original_analysis['file_sizes']):.1f} KB\n"
        metrics_text += "\n"
    
    if resized_analysis:
        metrics_text += f"Resized Dataset:\n"
        metrics_text += f"  Total Images: {resized_analysis['total_images']}\n"
        metrics_text += f"  Classes: {len(resized_analysis['classes'])}\n"
        if resized_analysis['file_sizes']:
            metrics_text += f"  Avg File Size: {np.mean(resized_analysis['file_sizes']):.1f} KB\n"
    
    axes[1, 2].text(0.1, 0.5, metrics_text, fontsize=10, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
                   transform=axes[1, 2].transAxes, verticalalignment='center')
    axes[1, 2].set_title('Summary Metrics')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Visual comparison saved to: {output_file}")

def main():
    """
    Main function untuk perbandingan dataset
    """
    print("📊 Dataset Comparison Tool")
    print("="*40)
    
    # Define dataset paths
    original_path = 'real_dataset'  # Sesuaikan dengan path dataset asli
    resized_path = 'resized_images_224'
    
    # Check for zip file
    if not os.path.exists(resized_path) and os.path.exists('resized_images_224.zip'):
        print("📦 Extracting resized dataset from zip...")
        import zipfile
        with zipfile.ZipFile('resized_images_224.zip', 'r') as zip_ref:
            zip_ref.extractall('temp_resized')
        resized_path = 'temp_resized'
    
    # Analyze datasets
    original_analysis = analyze_dataset_structure(original_path, "Original Dataset")
    resized_analysis = analyze_dataset_structure(resized_path, "Resized Dataset")
    
    if not original_analysis and not resized_analysis:
        print("❌ No datasets found to compare!")
        return
    
    # Print summary
    print(f"\n📊 COMPARISON SUMMARY:")
    if original_analysis:
        print(f"   Original: {original_analysis['total_images']} images, {len(original_analysis['classes'])} classes")
    if resized_analysis:
        print(f"   Resized: {resized_analysis['total_images']} images, {len(resized_analysis['classes'])} classes")
    
    # Create reports and visualizations
    if original_analysis and resized_analysis:
        create_comparison_report(original_analysis, resized_analysis)
        create_visual_comparison(original_analysis, resized_analysis)
        
        # Calculate success rate
        success_rate = (resized_analysis['total_images'] / original_analysis['total_images'] * 100) if original_analysis['total_images'] > 0 else 0
        print(f"\n✅ Resize Success Rate: {success_rate:.1f}%")
        
        if success_rate < 100:
            missing = original_analysis['total_images'] - resized_analysis['total_images']
            print(f"⚠️ {missing} images were not successfully resized")
    
    # Cleanup temporary directory
    if resized_path == 'temp_resized' and os.path.exists('temp_resized'):
        import shutil
        shutil.rmtree('temp_resized')
        print("🗑️ Cleaned up temporary files")
    
    print("\n🎉 Comparison completed!")

if __name__ == "__main__":
    main()
