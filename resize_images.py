import os
import cv2
import numpy as np

def resize_image_224x224(input_path, output_path):
    """
    Resize gambar ke 224x224 dengan padding hitam
    """
    try:
        # Baca gambar
        image = cv2.imread(input_path)
        if image is None:
            return False, f"Tidak bisa membaca file: {os.path.basename(input_path)}"

        # Get ukuran asli
        height, width = image.shape[:2]

        # Validasi ukuran gambar
        if width <= 0 or height <= 0:
            return False, f"Ukuran gambar tidak valid: {os.path.basename(input_path)} ({width}x{height})"

        # Hitung scale untuk fit ke 224x224
        scale = min(224 / width, 224 / height)

        # Ukuran baru
        new_width = int(width * scale)
        new_height = int(height * scale)

        # Resize gambar
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Buat canvas 224x224 hitam
        result = np.zeros((224, 224, 3), dtype=np.uint8)

        # Hitung posisi untuk center gambar
        x_offset = (224 - new_width) // 2
        y_offset = (224 - new_height) // 2

        # Paste gambar ke center
        result[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized

        # Save
        success = cv2.imwrite(output_path, result)
        if not success:
            return False, f"Gagal menyimpan: {os.path.basename(output_path)}"

        return True, f"Berhasil: {os.path.basename(input_path)} ({width}x{height} → 224x224)"

    except Exception as e:
        return False, f"Error pada {os.path.basename(input_path)}: {str(e)}"

def convert_directory(input_dir, output_dir):
    """
    Konversi semua gambar dalam direktori
    """
    if not os.path.exists(input_dir):
        print(f"Direktori tidak ditemukan: {input_dir}")
        return

    # Buat output directory
    os.makedirs(output_dir, exist_ok=True)

    total = 0
    success = 0
    failed_files = []  # List untuk menyimpan file yang gagal
    success_files = []  # List untuk menyimpan file yang berhasil

    print(f"Input: {input_dir}")
    print(f"Output: {output_dir}")
    print("-" * 60)

    # Proses setiap subdirektori (kelas)
    for class_name in os.listdir(input_dir):
        class_input_path = os.path.join(input_dir, class_name)

        if not os.path.isdir(class_input_path):
            continue

        print(f"\n📂 Processing: {class_name}")
        print("-" * 40)

        # Buat output directory untuk kelas
        class_output_path = os.path.join(output_dir, class_name)
        os.makedirs(class_output_path, exist_ok=True)

        class_success = 0
        class_failed = 0

        # Proses setiap gambar
        for filename in os.listdir(class_input_path):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')):
                input_file = os.path.join(class_input_path, filename)

                # Output filename (selalu .jpg)
                name = os.path.splitext(filename)[0]
                output_file = os.path.join(class_output_path, f"{name}.jpg")

                total += 1

                # Proses gambar
                is_success, message = resize_image_224x224(input_file, output_file)

                if is_success:
                    success += 1
                    class_success += 1
                    success_files.append(f"{class_name}/{filename}")
                    print(f"  ✅ {message}")
                else:
                    class_failed += 1
                    failed_files.append(f"{class_name}/{filename} - {message}")
                    print(f"  ❌ {message}")

        print(f"\n📊 {class_name} Summary:")
        print(f"  ✅ Berhasil: {class_success}")
        print(f"  ❌ Gagal: {class_failed}")

    # Print summary lengkap
    print(f"\n{'='*60}")
    print("📊 SUMMARY KONVERSI")
    print(f"{'='*60}")
    print(f"📈 Total processed: {total}")
    print(f"✅ Success: {success}")
    print(f"❌ Failed: {total - success}")
    print(f"📊 Success rate: {(success/total*100):.1f}%" if total > 0 else "0%")
    print(f"📁 Output directory: {output_dir}")

    # Tampilkan daftar file yang gagal
    if failed_files:
        print(f"\n❌ DAFTAR FILE YANG GAGAL ({len(failed_files)} files):")
        print("-" * 60)
        for i, failed_file in enumerate(failed_files, 1):
            print(f"{i:3d}. {failed_file}")
    else:
        print(f"\n🎉 Semua file berhasil dikonversi!")

    # Tampilkan beberapa file yang berhasil (opsional)
    if success_files and len(success_files) <= 10:
        print(f"\n✅ DAFTAR FILE YANG BERHASIL ({len(success_files)} files):")
        print("-" * 60)
        for i, success_file in enumerate(success_files, 1):
            print(f"{i:3d}. {success_file}")
    elif success_files:
        print(f"\n✅ {len(success_files)} file berhasil dikonversi (terlalu banyak untuk ditampilkan semua)")

    return {"total": total, "success": success, "failed": total - success, "failed_files": failed_files}

if __name__ == "__main__":
    print("Image Resizer ke 224x224 untuk MobileNetV2")
    print("="*50)
    
    # Default paths
    input_directory = "./data"
    output_directory = "./data_224x224"
    
    # Tanya user untuk path
    user_input = input(f"Input directory (default: {input_directory}): ").strip()
    if user_input:
        input_directory = user_input
    
    user_output = input(f"Output directory (default: {output_directory}): ").strip()
    if user_output:
        output_directory = user_output
    
    # Jalankan konversi
    convert_directory(input_directory, output_directory)
