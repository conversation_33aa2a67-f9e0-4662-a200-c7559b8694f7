#!/usr/bin/env python3
"""
Test script untuk Dataset Splitter
Script untuk menguji fungsi dataset splitter dengan data contoh.
"""

import os
import shutil
from pathlib import Path
from dataset_splitter import detect_dataset_structure, create_train_val_split, analyze_dataset

def create_sample_single_folder():
    """
    Membuat contoh dataset single folder untuk testing.
    """
    sample_dir = Path("sample_single_dataset")
    
    # Hapus jika sudah ada
    if sample_dir.exists():
        shutil.rmtree(sample_dir)
    
    # Buat direktori
    sample_dir.mkdir()
    
    # Buat file dummy (kosong)
    sample_files = [
        "bird1.jpg", "bird2.jpg", "bird3.jpg", "bird4.jpg", "bird5.jpg",
        "bird6.jpg", "bird7.jpg", "bird8.jpg", "bird9.jpg", "bird10.jpg"
    ]
    
    for filename in sample_files:
        (sample_dir / filename).touch()
    
    print(f"✅ Sample single folder dataset dibuat di: {sample_dir}")
    return str(sample_dir)

def create_sample_multiclass_dataset():
    """
    Membuat contoh dataset multi-class untuk testing.
    """
    sample_dir = Path("sample_multiclass_dataset")
    
    # Hapus jika sudah ada
    if sample_dir.exists():
        shutil.rmtree(sample_dir)
    
    # Buat direktori
    sample_dir.mkdir()
    
    # Buat kelas-kelas
    classes = {
        "Lonchura_leucogastroides": ["img1.jpg", "img2.jpg", "img3.jpg", "img4.jpg", "img5.jpg"],
        "Lonchura_maja": ["img1.jpg", "img2.jpg", "img3.jpg", "img4.jpg"],
        "Lonchura_punctulata": ["img1.jpg", "img2.jpg", "img3.jpg", "img4.jpg", "img5.jpg", "img6.jpg"],
        "Passer_montanus": ["img1.jpg", "img2.jpg", "img3.jpg"]
    }
    
    for class_name, files in classes.items():
        class_dir = sample_dir / class_name
        class_dir.mkdir()
        
        for filename in files:
            (class_dir / filename).touch()
    
    print(f"✅ Sample multi-class dataset dibuat di: {sample_dir}")
    return str(sample_dir)

def test_detection():
    """
    Test fungsi deteksi struktur dataset.
    """
    print("\n" + "="*60)
    print("🧪 TESTING DETEKSI STRUKTUR DATASET")
    print("="*60)
    
    # Test single folder
    single_dir = create_sample_single_folder()
    structure, info = detect_dataset_structure(single_dir)
    print(f"\n📁 {single_dir}:")
    print(f"  Struktur: {structure}")
    print(f"  Info: {info}")
    
    # Test multi-class
    multi_dir = create_sample_multiclass_dataset()
    structure, info = detect_dataset_structure(multi_dir)
    print(f"\n📁 {multi_dir}:")
    print(f"  Struktur: {structure}")
    print(f"  Info: {info}")

def test_splitting():
    """
    Test fungsi pembagian dataset.
    """
    print("\n" + "="*60)
    print("🧪 TESTING PEMBAGIAN DATASET")
    print("="*60)
    
    # Test single folder
    print("\n1. Testing Single Folder:")
    single_dir = "sample_single_dataset"
    if Path(single_dir).exists():
        result = create_train_val_split(
            single_dir, 
            "output_single", 
            train_ratio=0.7, 
            random_seed=42, 
            preview=False
        )
        print(f"Result: {result}")
    
    # Test multi-class
    print("\n2. Testing Multi-Class:")
    multi_dir = "sample_multiclass_dataset"
    if Path(multi_dir).exists():
        result = create_train_val_split(
            multi_dir, 
            "output_multiclass", 
            train_ratio=0.8, 
            random_seed=42, 
            preview=False
        )
        print(f"Result: {result}")

def test_analysis():
    """
    Test fungsi analisis dataset.
    """
    print("\n" + "="*60)
    print("🧪 TESTING ANALISIS DATASET")
    print("="*60)
    
    # Analisis single folder output
    if Path("output_single").exists():
        print("\n1. Analisis Single Folder Output:")
        analyze_dataset("output_single")
    
    # Analisis multi-class output
    if Path("output_multiclass").exists():
        print("\n2. Analisis Multi-Class Output:")
        analyze_dataset("output_multiclass")

def cleanup():
    """
    Bersihkan file-file test.
    """
    dirs_to_remove = [
        "sample_single_dataset",
        "sample_multiclass_dataset", 
        "output_single",
        "output_multiclass"
    ]
    
    print("\n" + "="*60)
    print("🧹 CLEANUP")
    print("="*60)
    
    for dir_name in dirs_to_remove:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"🗑️  Dihapus: {dir_name}")

def main():
    print("🧪 DATASET SPLITTER - TEST SUITE")
    print("="*60)
    print("Script ini akan menguji semua fungsi dataset splitter.")
    print()
    
    try:
        # Jalankan semua test
        test_detection()
        test_splitting()
        test_analysis()
        
        print("\n" + "="*60)
        print("✅ SEMUA TEST SELESAI!")
        print("="*60)
        
        # Tanya apakah ingin cleanup
        cleanup_choice = input("\n❓ Hapus file-file test? (y/n): ").strip().lower()
        if cleanup_choice in ['y', 'yes', 'ya']:
            cleanup()
        else:
            print("📁 File-file test dibiarkan untuk inspeksi manual.")
            
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
