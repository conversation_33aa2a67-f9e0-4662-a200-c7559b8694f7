#!/usr/bin/env python3
"""
Rename Generator Script
Mengubah nama file dalam folder menjadi format: "NamaFolder 1", "NamaFolder 2", dst.
Mendukung semua jenis file dan tidak ada batas jumlah file.
"""

import os
import sys
from pathlib import Path
import argparse

def get_file_extension(filename):
    """Mendapatkan ekstensi file"""
    return Path(filename).suffix

def rename_files_in_folder(folder_path, new_name=None, start_number=1, dry_run=False):
    """
    Rename semua file dalam folder dengan format: "nama angka.ekstensi"
    
    Args:
        folder_path (str): Path ke folder yang berisi file
        new_name (str): Nama baru untuk file (default: nama folder)
        start_number (int): Nomor awal (default: 1)
        dry_run (bool): Jika True, hanya menampilkan preview tanpa melakukan rename
    
    Returns:
        tuple: (jumlah_file_berhasil, jumlah_file_gagal, list_error)
    """
    
    folder_path = Path(folder_path)
    
    if not folder_path.exists():
        print(f"❌ Error: Folder '{folder_path}' tidak ditemukan!")
        return 0, 0, ["Folder tidak ditemukan"]
    
    if not folder_path.is_dir():
        print(f"❌ Error: '{folder_path}' bukan folder!")
        return 0, 0, ["Path bukan folder"]
    
    # Jika new_name tidak diberikan, gunakan nama folder
    if new_name is None:
        new_name = folder_path.name
    
    # Dapatkan semua file dalam folder (tidak termasuk subfolder)
    files = [f for f in folder_path.iterdir() if f.is_file()]
    
    if not files:
        print(f"📁 Folder '{folder_path}' kosong atau tidak ada file!")
        return 0, 0, ["Tidak ada file dalam folder"]
    
    # Urutkan file berdasarkan nama untuk konsistensi
    files.sort(key=lambda x: x.name.lower())
    
    print(f"📁 Folder: {folder_path}")
    print(f"🏷️  Nama baru: {new_name}")
    print(f"📊 Total file: {len(files)}")
    print(f"🔢 Mulai dari: {start_number}")
    
    if dry_run:
        print("\n🔍 PREVIEW MODE - Tidak ada file yang akan direname:")
        print("-" * 60)
    else:
        print("\n🔄 Memulai proses rename:")
        print("-" * 60)
    
    success_count = 0
    error_count = 0
    errors = []
    
    for i, file_path in enumerate(files):
        try:
            # Dapatkan ekstensi file
            extension = get_file_extension(file_path.name)
            
            # Buat nama file baru
            new_number = start_number + i
            new_filename = f"{new_name} {new_number}{extension}"
            new_file_path = folder_path / new_filename
            
            # Tampilkan informasi
            if dry_run:
                print(f"  {file_path.name} → {new_filename}")
            else:
                # Cek apakah file dengan nama baru sudah ada
                if new_file_path.exists() and new_file_path != file_path:
                    error_msg = f"File '{new_filename}' sudah ada!"
                    print(f"  ❌ {file_path.name} → {error_msg}")
                    errors.append(error_msg)
                    error_count += 1
                    continue
                
                # Lakukan rename
                file_path.rename(new_file_path)
                print(f"  ✅ {file_path.name} → {new_filename}")
                success_count += 1
                
        except Exception as e:
            error_msg = f"Error pada '{file_path.name}': {str(e)}"
            print(f"  ❌ {error_msg}")
            errors.append(error_msg)
            error_count += 1
    
    # Tampilkan ringkasan
    print("-" * 60)
    if dry_run:
        print(f"📋 PREVIEW SELESAI")
        print(f"   Total file yang akan direname: {len(files)}")
    else:
        print(f"📋 RENAME SELESAI")
        print(f"   ✅ Berhasil: {success_count}")
        print(f"   ❌ Gagal: {error_count}")
        
        if errors:
            print(f"\n⚠️  Error yang terjadi:")
            for error in errors:
                print(f"   - {error}")
    
    return success_count, error_count, errors

def main():
    parser = argparse.ArgumentParser(
        description="Rename Generator - Mengubah nama file dalam folder",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Contoh penggunaan:
  python rename_generator.py "ResizedDatasetUnknown/Anisolabididae"
  python rename_generator.py "path/to/folder" --name "Anisolabididae" --start 1
  python rename_generator.py "path/to/folder" --name "MyFiles" --start 100 --preview
        """
    )
    
    parser.add_argument("folder", help="Path ke folder yang berisi file")
    parser.add_argument("--name", "-n", help="Nama baru untuk file (default: nama folder)")
    parser.add_argument("--start", "-s", type=int, default=1, help="Nomor awal (default: 1)")
    parser.add_argument("--preview", "-p", action="store_true", help="Preview saja, tidak melakukan rename")
    
    args = parser.parse_args()
    
    print("🔄 RENAME GENERATOR")
    print("=" * 50)
    
    success, failed, errors = rename_files_in_folder(
        args.folder, 
        args.name, 
        args.start, 
        args.preview
    )
    
    if not args.preview:
        if failed == 0:
            print(f"\n🎉 Semua file berhasil direname!")
        else:
            print(f"\n⚠️  Beberapa file gagal direname. Periksa error di atas.")

def interactive_mode():
    """Mode interaktif untuk kemudahan penggunaan"""
    print("🔄 RENAME GENERATOR - MODE INTERAKTIF")
    print("=" * 50)
    
    # Input folder path
    while True:
        folder_path = input("📁 Masukkan path folder: ").strip().strip('"')
        if folder_path and Path(folder_path).exists():
            break
        print("❌ Folder tidak ditemukan! Coba lagi.")
    
    # Input nama baru (opsional)
    new_name = input("🏷️  Nama baru (kosong = nama folder): ").strip()
    if not new_name:
        new_name = None
    
    # Input nomor awal
    while True:
        try:
            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1
            break
        except ValueError:
            print("❌ Masukkan angka yang valid!")
    
    # Preview dulu
    print("\n" + "="*50)
    rename_files_in_folder(folder_path, new_name, start_num, dry_run=True)
    
    # Konfirmasi
    confirm = input("\n❓ Lanjutkan rename? (y/n): ").strip().lower()
    if confirm in ['y', 'yes', 'ya']:
        print("\n" + "="*50)
        rename_files_in_folder(folder_path, new_name, start_num, dry_run=False)
    else:
        print("❌ Rename dibatalkan.")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # Jika tidak ada argument, jalankan mode interaktif
        interactive_mode()
    else:
        # Jalankan dengan argument
        main()
