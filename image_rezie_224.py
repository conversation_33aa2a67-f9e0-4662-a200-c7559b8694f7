import os
import cv2
import numpy as np
from multiprocessing import Pool
from tqdm import tqdm
import zipfile
import glob
from PIL import Image
import warnings
import shutil  # <-- 1. IMPORT MODUL SHUTIL


# Suppress PIL warnings for corrupted images
warnings.filterwarnings("ignore", category=UserWarning)


DATASET_PATH = r'class'
IMG_SIZE = 224


def zip_and_remove(path):
    """Zip folder and remove original files"""
    ziph = zipfile.ZipFile(f'{path}.zip', 'w', zipfile.ZIP_DEFLATED)

    for root, dirs, files in os.walk(path):
        for file in tqdm(files, desc="Zipping files"):
            file_path = os.path.join(root, file)
            ziph.write(file_path, os.path.relpath(file_path, path))
            os.remove(file_path)

    ziph.close()
    # Remove empty directories
    for root, dirs, files in os.walk(path, topdown=False):
        for dir in dirs:
            dir_path = os.path.join(root, dir)
            try:
                os.rmdir(dir_path)
            except OSError:
                pass
    try:
        os.rmdir(path)
    except OSError:
        pass


def img_proc(img_info):
    """Process single image - resize to 224x224 with robust error handling"""
    input_path, output_path = img_info

    # Try OpenCV first
    try:
        img = cv2.imread(input_path)
        if img is not None:
            # Check if image is valid (not empty)
            if img.shape[0] > 0 and img.shape[1] > 0:
                img_resized = cv2.resize(img, (IMG_SIZE, IMG_SIZE))
                cv2.imwrite(output_path, img_resized)
                return True
    except Exception as cv_error:
        # If OpenCV fails, try PIL as fallback
        try:
            with Image.open(input_path) as pil_img:
                # Convert to RGB if necessary
                if pil_img.mode != 'RGB':
                    pil_img = pil_img.convert('RGB')

                # Resize image
                pil_img_resized = pil_img.resize((IMG_SIZE, IMG_SIZE), Image.Resampling.LANCZOS)

                # Convert PIL to OpenCV format and save
                img_array = np.array(pil_img_resized)
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                cv2.imwrite(output_path, img_bgr)
                return True

        except Exception as pil_error:
            print(f"❌ Failed to process {input_path}")
            print(f"   OpenCV error: {cv_error}")
            print(f"   PIL error: {pil_error}")
            return False

    print(f"⚠️ Warning: Could not read image {input_path} (empty or invalid)")
    return False


def imap_unordered_bar(func, args, n_processes: int = 4):
    """Process images with multiprocessing and progress bar"""
    p = Pool(n_processes, maxtasksperchild=100)
    res_list = []
    with tqdm(total=len(args), desc="Processing images") as pbar:
        for i, res in enumerate(p.imap_unordered(func, args)):
            pbar.update()
            res_list.append(res)
    pbar.close()
    p.close()
    p.join()
    return res_list


def validate_images(image_paths):
    """Validate images before processing"""
    print("🔍 Validating images...")
    valid_images = []
    corrupted_images = []

    for img_path in tqdm(image_paths, desc="Validating"):
        try:
            # Try to open with PIL first (more robust)
            with Image.open(img_path) as img:
                # Check if image has valid dimensions
                if img.size[0] > 0 and img.size[1] > 0:
                    valid_images.append(img_path)
                else:
                    corrupted_images.append((img_path, "Invalid dimensions"))
        except Exception as e:
            corrupted_images.append((img_path, str(e)))

    print(f"✅ Valid images: {len(valid_images)}")
    print(f"❌ Corrupted images: {len(corrupted_images)}")

    if corrupted_images:
        print("\n🚨 Corrupted images found:")
        for img_path, error in corrupted_images[:10]:  # Show first 10
            print(f"   {img_path}: {error}")
        if len(corrupted_images) > 10:
            print(f"   ... and {len(corrupted_images) - 10} more")

    return valid_images, corrupted_images

def main():
    print("🖼️ Image Resize Tool - Enhanced Version")
    print("="*50)

    output_dir = 'resized_images_224'
    zip_file_path = f'{output_dir}.zip'

    # <-- 2. TAMBAHKAN BLOK PEMBERSIHAN DI SINI
    print(f"🧹 Cleaning up previous run...")
    # Hapus direktori output lama jika ada (beserta isinya)
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
        print(f"   Removed old directory: {output_dir}")

    # Hapus file zip lama jika ada
    if os.path.exists(zip_file_path):
        os.remove(zip_file_path)
        print(f"   Removed old zip file: {zip_file_path}")
    
    print("✨ Cleanup complete.")
    # SELESAI BLOK PEMBERSIHAN -->

    # Check if dataset path exists
    if not os.path.exists(DATASET_PATH):
        print(f"❌ Dataset path not found: {DATASET_PATH}")
        return

    # Create output directory
    os.makedirs(output_dir) # Sekarang kita bisa langsung membuat folder baru yang pasti kosong

    # Get all image files from dataset
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    all_images = []

    print(f"📁 Scanning dataset: {DATASET_PATH}")
    for ext in image_extensions:
        pattern = os.path.join(DATASET_PATH, '**', ext)
        found_images = glob.glob(pattern, recursive=True)
        all_images.extend(found_images)
        if found_images:
            print(f"   Found {len(found_images)} {ext} files")

    print(f"\n📊 Total images found: {len(all_images)}")

    if not all_images:
        print("❌ No images found! Please check your dataset path.")
        return

    # Validate images before processing
    valid_images, corrupted_images = validate_images(all_images)

    if not valid_images:
        print("❌ No valid images found to process!")
        return

    # Prepare image processing arguments (only for valid images)
    img_args = []
    print(f"\n🔄 Preparing to process {len(valid_images)} valid images...")

    for img_path in valid_images:
        # Get relative path from dataset root
        rel_path = os.path.relpath(img_path, DATASET_PATH)
        # Create output path maintaining folder structure
        output_path = os.path.join(output_dir, rel_path)

        # Create output directory if it doesn't exist
        output_folder = os.path.dirname(output_path)
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        img_args.append((img_path, output_path))

    # Process images
    if img_args:
        print(f"\n🚀 Starting image processing...")
        results = imap_unordered_bar(img_proc, img_args, n_processes=4)
        successful = sum(results)
        failed = len(results) - successful

        print(f"\n📊 PROCESSING SUMMARY:")
        print(f"   ✅ Successfully processed: {successful}/{len(img_args)} images")
        print(f"   ❌ Failed to process: {failed} images")
        print(f"   🚨 Corrupted/skipped: {len(corrupted_images)} images")
        print(f"   📊 Total success rate: {(successful/len(all_images)*100):.1f}%")

        if successful > 0:
            # Create zip file
            print(f"\n📦 Creating zip file...")
            zip_and_remove(output_dir)
            print(f"✅ Completed! Check {output_dir}.zip")

            # Save processing report
            with open('resize_report.txt', 'w') as f:
                f.write("Image Resize Processing Report\n")
                f.write("="*40 + "\n\n")
                f.write(f"Total images found: {len(all_images)}\n")
                f.write(f"Valid images: {len(valid_images)}\n")
                f.write(f"Successfully processed: {successful}\n")
                f.write(f"Failed to process: {failed}\n")
                f.write(f"Corrupted/skipped: {len(corrupted_images)}\n")
                f.write(f"Success rate: {(successful/len(all_images)*100):.1f}%\n\n")

                if corrupted_images:
                    f.write("Corrupted Images:\n")
                    f.write("-"*20 + "\n")
                    for img_path, error in corrupted_images:
                        f.write(f"{img_path}: {error}\n")

            print(f"📋 Processing report saved to: resize_report.txt")
        else:
            print("❌ No images were successfully processed!")
    else:
        print("❌ No valid images found to process!")


if __name__ == '__main__':
    main()