#!/usr/bin/env python3
"""
Validate Resized Images
Script untuk memvalidasi hasil resize dan menganalisis gambar yang gagal diproses
"""

import os
import zipfile
import glob
from PIL import Image
import cv2
import numpy as np
from tqdm import tqdm
from collections import defaultdict, Counter
import matplotlib.pyplot as plt

def extract_and_analyze_zip(zip_path, extract_to='temp_extracted'):
    """
    Extract zip file dan analisis isinya
    """
    print(f"📦 Extracting {zip_path}...")
    
    if not os.path.exists(zip_path):
        print(f"❌ Zip file not found: {zip_path}")
        return None
    
    # Extract zip file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
    
    print(f"✅ Extracted to: {extract_to}")
    return extract_to

def analyze_resized_dataset(dataset_path):
    """
    Analisis dataset yang sudah di-resize
    """
    print(f"🔍 Analyzing resized dataset: {dataset_path}")
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset path not found: {dataset_path}")
        return None
    
    analysis = {
        'total_images': 0,
        'classes': defaultdict(int),
        'sizes': Counter(),
        'formats': Counter(),
        'valid_images': 0,
        'invalid_images': [],
        'size_issues': []
    }
    
    # Get all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    all_images = []
    
    for ext in image_extensions:
        pattern = os.path.join(dataset_path, '**', ext)
        all_images.extend(glob.glob(pattern, recursive=True))
    
    analysis['total_images'] = len(all_images)
    print(f"📊 Found {len(all_images)} images")
    
    # Analyze each image
    for img_path in tqdm(all_images, desc="Analyzing images"):
        try:
            # Get class name from path
            rel_path = os.path.relpath(img_path, dataset_path)
            class_name = rel_path.split(os.sep)[0] if os.sep in rel_path else 'unknown'
            analysis['classes'][class_name] += 1
            
            # Check image properties
            with Image.open(img_path) as img:
                width, height = img.size
                analysis['sizes'][(width, height)] += 1
                analysis['formats'][img.format] += 1
                
                # Check if properly resized to 224x224
                if width == 224 and height == 224:
                    analysis['valid_images'] += 1
                else:
                    analysis['size_issues'].append((img_path, width, height))
                
        except Exception as e:
            analysis['invalid_images'].append((img_path, str(e)))
    
    return analysis

def create_analysis_report(analysis, output_file='resize_validation_report.txt'):
    """
    Buat laporan analisis
    """
    print(f"📋 Creating analysis report...")
    
    with open(output_file, 'w') as f:
        f.write("RESIZED DATASET VALIDATION REPORT\n")
        f.write("="*50 + "\n\n")
        
        # Summary
        f.write("SUMMARY:\n")
        f.write("-"*20 + "\n")
        f.write(f"Total images: {analysis['total_images']}\n")
        f.write(f"Valid 224x224 images: {analysis['valid_images']}\n")
        f.write(f"Images with size issues: {len(analysis['size_issues'])}\n")
        f.write(f"Invalid/corrupted images: {len(analysis['invalid_images'])}\n")
        
        success_rate = (analysis['valid_images'] / analysis['total_images'] * 100) if analysis['total_images'] > 0 else 0
        f.write(f"Success rate: {success_rate:.1f}%\n\n")
        
        # Class distribution
        f.write("CLASS DISTRIBUTION:\n")
        f.write("-"*20 + "\n")
        for class_name, count in analysis['classes'].items():
            f.write(f"{class_name}: {count} images\n")
        f.write("\n")
        
        # Size distribution
        f.write("SIZE DISTRIBUTION:\n")
        f.write("-"*20 + "\n")
        for (width, height), count in analysis['sizes'].most_common():
            f.write(f"{width}x{height}: {count} images\n")
        f.write("\n")
        
        # Format distribution
        f.write("FORMAT DISTRIBUTION:\n")
        f.write("-"*20 + "\n")
        for format_name, count in analysis['formats'].items():
            f.write(f"{format_name}: {count} images\n")
        f.write("\n")
        
        # Size issues
        if analysis['size_issues']:
            f.write("SIZE ISSUES:\n")
            f.write("-"*20 + "\n")
            for img_path, width, height in analysis['size_issues']:
                f.write(f"{img_path}: {width}x{height}\n")
            f.write("\n")
        
        # Invalid images
        if analysis['invalid_images']:
            f.write("INVALID IMAGES:\n")
            f.write("-"*20 + "\n")
            for img_path, error in analysis['invalid_images']:
                f.write(f"{img_path}: {error}\n")
    
    print(f"✅ Report saved to: {output_file}")

def create_sample_grid(dataset_path, output_file='resized_samples.png', samples_per_class=4):
    """
    Buat grid sample gambar yang sudah di-resize
    """
    print(f"🖼️ Creating sample grid...")
    
    # Get classes
    classes = []
    for item in os.listdir(dataset_path):
        item_path = os.path.join(dataset_path, item)
        if os.path.isdir(item_path):
            classes.append(item)
    
    if not classes:
        print("❌ No classes found")
        return
    
    fig, axes = plt.subplots(len(classes), samples_per_class, 
                            figsize=(samples_per_class * 2, len(classes) * 2))
    
    if len(classes) == 1:
        axes = axes.reshape(1, -1)
    
    for i, class_name in enumerate(classes):
        class_path = os.path.join(dataset_path, class_name)
        image_files = [f for f in os.listdir(class_path) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
        
        # Take first few samples
        sample_files = image_files[:samples_per_class]
        
        for j, img_file in enumerate(sample_files):
            if j >= samples_per_class:
                break
                
            img_path = os.path.join(class_path, img_file)
            try:
                img = Image.open(img_path)
                axes[i, j].imshow(img)
                axes[i, j].set_title(f'{class_name}\n{img.size[0]}x{img.size[1]}', fontsize=8)
                axes[i, j].axis('off')
            except Exception as e:
                axes[i, j].text(0.5, 0.5, f'Error\n{str(e)[:15]}...', 
                               ha='center', va='center', transform=axes[i, j].transAxes,
                               fontsize=8, color='red')
                axes[i, j].axis('off')
        
        # Hide unused subplots
        for j in range(len(sample_files), samples_per_class):
            axes[i, j].axis('off')
    
    plt.suptitle('Resized Images Sample (224x224)', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Sample grid saved to: {output_file}")

def fix_size_issues(dataset_path, analysis):
    """
    Perbaiki gambar yang ukurannya tidak sesuai
    """
    if not analysis['size_issues']:
        print("✅ No size issues to fix!")
        return
    
    print(f"🔧 Fixing {len(analysis['size_issues'])} images with size issues...")
    
    fixed_count = 0
    for img_path, width, height in tqdm(analysis['size_issues'], desc="Fixing sizes"):
        try:
            with Image.open(img_path) as img:
                # Resize to 224x224
                img_resized = img.resize((224, 224), Image.Resampling.LANCZOS)
                
                # Save back
                if img_path.lower().endswith('.png'):
                    img_resized.save(img_path, 'PNG', optimize=True)
                else:
                    img_resized.save(img_path, 'JPEG', quality=95, optimize=True)
                
                fixed_count += 1
                
        except Exception as e:
            print(f"❌ Failed to fix {img_path}: {e}")
    
    print(f"✅ Fixed {fixed_count}/{len(analysis['size_issues'])} images")

def main():
    """
    Main function untuk validasi hasil resize
    """
    print("🔍 Resized Images Validator")
    print("="*40)
    
    # Check for zip file first
    zip_file = 'resized_images_224.zip'
    extracted_dir = 'temp_extracted'
    
    if os.path.exists(zip_file):
        print(f"📦 Found zip file: {zip_file}")
        dataset_path = extract_and_analyze_zip(zip_file, extracted_dir)
        if not dataset_path:
            return
    elif os.path.exists('resized_images_224'):
        print(f"📁 Found extracted directory: resized_images_224")
        dataset_path = 'resized_images_224'
    else:
        print("❌ No resized dataset found!")
        print("Please run image_rezie_224.py first.")
        return
    
    # Analyze dataset
    analysis = analyze_resized_dataset(dataset_path)
    
    if analysis:
        # Print summary
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"   Total images: {analysis['total_images']}")
        print(f"   Valid 224x224 images: {analysis['valid_images']}")
        print(f"   Size issues: {len(analysis['size_issues'])}")
        print(f"   Invalid images: {len(analysis['invalid_images'])}")
        
        success_rate = (analysis['valid_images'] / analysis['total_images'] * 100) if analysis['total_images'] > 0 else 0
        print(f"   Success rate: {success_rate:.1f}%")
        
        # Create report
        create_analysis_report(analysis)
        
        # Create sample grid
        create_sample_grid(dataset_path)
        
        # Ask if user wants to fix size issues
        if analysis['size_issues']:
            fix_choice = input(f"\n🔧 Fix {len(analysis['size_issues'])} images with size issues? (y/n): ")
            if fix_choice.lower() == 'y':
                fix_size_issues(dataset_path, analysis)
                
                # Re-analyze after fixing
                print("\n🔄 Re-analyzing after fixes...")
                analysis_fixed = analyze_resized_dataset(dataset_path)
                if analysis_fixed:
                    success_rate_fixed = (analysis_fixed['valid_images'] / analysis_fixed['total_images'] * 100)
                    print(f"✅ New success rate: {success_rate_fixed:.1f}%")
        
        # Cleanup extracted directory if it was created
        if dataset_path == extracted_dir and os.path.exists(extracted_dir):
            import shutil
            cleanup_choice = input(f"\n🗑️ Remove temporary extracted directory? (y/n): ")
            if cleanup_choice.lower() == 'y':
                shutil.rmtree(extracted_dir)
                print(f"✅ Cleaned up {extracted_dir}")
    
    print("\n🎉 Validation completed!")

if __name__ == "__main__":
    main()
