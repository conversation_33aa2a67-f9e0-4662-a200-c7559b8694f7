# Klasifikasi Burung dengan MobileNetV2 - Cross Validation

## Perubahan yang Dilakukan

File `klasifikasi_burung_mobilenetv2_cv.py` telah dimodifikasi untuk menggunakan **5-Fold Cross Validation** sambil mempertahankan pendekatan **Transfer Learning** dengan MobileNetV2.

## Fitur Utama

### 1. Cross Validation dengan StratifiedKFold
- Menggunakan 5-fold cross validation untuk evaluasi yang lebih robust
- StratifiedKFold memastikan distribusi kelas yang seimbang di setiap fold
- Menggabungkan data training dan validation untuk cross validation

### 2. Transfer Learning Tetap Dipertahankan
- Menggunakan MobileNetV2 pre-trained pada ImageNet
- Freeze layer-layer awal untuk mempertahankan fitur yang sudah dipelajari
- Fine-tuning pada layer-layer akhir
- Custom head dengan GlobalAveragePooling2D dan Dense layers

### 3. Data Augmentation
- Rotasi, shift, shear, zoom, dan flip untuk meningkatkan variasi data
- Diterapkan pada setiap fold training

### 4. Evaluasi Komprehensif
- Akurasi per fold
- Mean dan standard deviation dari cross validation
- Confusion matrix dari semua prediksi
- Classification report dengan precision, recall, dan F1-score
- Visualisasi training history untuk semua fold

## Struktur Kode

### Fungsi Utama:

1. **`load_data_for_cv()`**
   - Memuat dan menggabungkan data dari folder train dan validation
   - Membuat mapping label ke indeks
   - Mengembalikan path gambar dan label untuk cross validation

2. **`create_mobilenetv2_model()`**
   - Membuat model MobileNetV2 dengan transfer learning
   - Freeze layer pre-trained
   - Menambahkan custom head
   - Kompilasi model dengan optimizer dan learning rate scheduling

3. **`load_and_preprocess_image()`**
   - Memuat dan preprocessing gambar individual
   - Resize ke 224x224 dan normalisasi

4. **`perform_cross_validation()`**
   - Implementasi 5-fold cross validation
   - Training model untuk setiap fold
   - Evaluasi dan pengumpulan hasil

5. **`plot_results()`**
   - Visualisasi confusion matrix
   - Plot training history untuk semua fold
   - Bar chart akurasi cross validation

6. **`train_final_model()`**
   - Training model final pada seluruh data
   - Untuk deployment setelah cross validation

## Keunggulan Pendekatan Ini

### 1. Evaluasi yang Lebih Robust
- Cross validation memberikan estimasi performa yang lebih reliable
- Mengurangi bias dari single train-validation split
- Memberikan confidence interval untuk akurasi

### 2. Transfer Learning Optimal
- Memanfaatkan knowledge dari ImageNet
- Faster convergence dan better performance
- Efisien untuk dataset yang relatif kecil

### 3. Comprehensive Analysis
- Detailed performance metrics per fold
- Visual analysis dengan multiple plots
- Classification report untuk setiap kelas

## Cara Penggunaan

1. **Persiapan Data**
   - Pastikan data tersimpan di Google Drive dalam format zip
   - Struktur folder: images/train/ dan images/validation/

2. **Menjalankan Kode**
   ```python
   # Jalankan seluruh script
   python klasifikasi_burung_mobilenetv2_cv.py
   ```

3. **Output yang Dihasilkan**
   - Cross validation results dengan akurasi per fold
   - Confusion matrix dan classification report
   - Visualisasi training history
   - Model final tersimpan sebagai 'birds_classification_cv.h5'

## Parameter yang Dapat Disesuaikan

```python
INPUT_SHAPE = (224, 224, 3)  # Ukuran input gambar
NUM_CLASSES = 5              # Jumlah kelas burung
BATCH_SIZE = 128             # Batch size untuk training
N_SPLITS = 5                 # Jumlah fold untuk cross validation
EPOCHS = 30                  # Maksimum epochs per fold
```

## Hasil yang Diharapkan

- **Mean CV Accuracy**: Akurasi rata-rata dari 5 fold
- **Standard Deviation**: Variabilitas performa antar fold
- **Confusion Matrix**: Analisis kesalahan klasifikasi
- **Training Curves**: Monitoring overfitting/underfitting
- **Final Model**: Model siap deploy dengan performa optimal

## Keuntungan vs Pendekatan Sebelumnya

| Aspek | Sebelumnya | Dengan Cross Validation |
|-------|------------|------------------------|
| Evaluasi | Single split | 5-fold validation |
| Reliability | Bias terhadap split tertentu | Lebih robust dan general |
| Confidence | Satu nilai akurasi | Mean ± std deviation |
| Analysis | Terbatas | Comprehensive metrics |
| Model Selection | Subjektif | Berdasarkan CV performance |

## Catatan Penting

- Cross validation membutuhkan waktu training 5x lebih lama
- Memory usage lebih tinggi karena memuat semua data
- Hasil lebih reliable untuk evaluasi model
- Transfer learning tetap dipertahankan untuk efisiensi training
