# -*- coding: utf-8 -*-
"""
Cross Validation Sederhana dengan Pendekatan Procedural Programming
Versi yang lebih mudah dipahami untuk pembelajaran
Tanpa menggunakan function - langkah demi langkah
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, confusion_matrix
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.layers import GlobalAveragePooling2D, Dense, Dropout
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from PIL import Image

print("=== CROSS VALIDATION SEDERHANA - PENDEKATAN PROCEDURAL ===")

# ===== LANGKAH 1: SETUP AWAL =====
print("\nLANGKAH 1: Setup awal")

# Parameter dasar
IMG_SIZE = 224
NUM_CLASSES = 5
BATCH_SIZE = 32
N_FOLDS = 3  # Menggunakan 3 fold untuk contoh yang lebih cepat

print(f"Ukuran gambar: {IMG_SIZE}x{IMG_SIZE}")
print(f"Jumlah kelas: {NUM_CLASSES}")
print(f"Batch size: {BATCH_SIZE}")
print(f"Jumlah fold: {N_FOLDS}")

# ===== LANGKAH 2: SIMULASI DATA (UNTUK CONTOH) =====
print("\nLANGKAH 2: Persiapan data simulasi")

# Untuk contoh, kita buat data simulasi
# Dalam implementasi nyata, ganti dengan loading data sesungguhnya
np.random.seed(42)

# Simulasi 500 gambar dengan 5 kelas
NUM_SAMPLES = 500
X_data = np.random.rand(NUM_SAMPLES, IMG_SIZE, IMG_SIZE, 3)
y_data = np.random.randint(0, NUM_CLASSES, NUM_SAMPLES)

print(f"Data simulasi dibuat: {X_data.shape}")
print(f"Label simulasi dibuat: {y_data.shape}")

# Distribusi kelas
unique, counts = np.unique(y_data, return_counts=True)
print("Distribusi kelas:")
for i, count in enumerate(counts):
    print(f"  Kelas {i}: {count} sampel")

# ===== LANGKAH 3: INISIALISASI CROSS VALIDATION =====
print("\nLANGKAH 3: Inisialisasi Cross Validation")

# Buat StratifiedKFold
skf = StratifiedKFold(n_splits=N_FOLDS, shuffle=True, random_state=42)

# Variabel untuk menyimpan hasil
fold_accuracies = []
fold_losses = []
all_predictions = []
all_true_labels = []

print(f"StratifiedKFold dengan {N_FOLDS} fold siap")

# ===== LANGKAH 4: LOOP CROSS VALIDATION =====
print("\nLANGKAH 4: Memulai Cross Validation")

current_fold = 0

# Iterasi untuk setiap fold
for train_index, val_index in skf.split(X_data, y_data):
    current_fold += 1
    print(f"\n--- MEMPROSES FOLD {current_fold}/{N_FOLDS} ---")
    
    # LANGKAH 4.1: Bagi data untuk fold ini
    print(f"Membagi data untuk fold {current_fold}")
    
    X_train_fold = X_data[train_index]
    X_val_fold = X_data[val_index]
    y_train_fold = y_data[train_index]
    y_val_fold = y_data[val_index]
    
    print(f"  Data training: {X_train_fold.shape}")
    print(f"  Data validasi: {X_val_fold.shape}")
    
    # Konversi label ke categorical
    y_train_categorical = tf.keras.utils.to_categorical(y_train_fold, NUM_CLASSES)
    y_val_categorical = tf.keras.utils.to_categorical(y_val_fold, NUM_CLASSES)
    
    # LANGKAH 4.2: Buat model untuk fold ini
    print(f"Membuat model untuk fold {current_fold}")
    
    # Load MobileNetV2 base model
    base_model = MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_shape=(IMG_SIZE, IMG_SIZE, 3)
    )
    
    # Freeze base model
    base_model.trainable = False
    
    # Tambahkan layer klasifikasi
    inputs = base_model.input
    x = base_model.output
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.3)(x)
    x = Dense(128, activation='relu')(x)
    x = Dropout(0.3)(x)
    outputs = Dense(NUM_CLASSES, activation='softmax')(x)
    
    # Buat model lengkap
    model = Model(inputs, outputs)
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print(f"  Model fold {current_fold} siap")
    
    # LANGKAH 4.3: Training model
    print(f"Training model fold {current_fold}")
    
    # Training dengan epoch yang sedikit untuk contoh
    history = model.fit(
        X_train_fold, y_train_categorical,
        validation_data=(X_val_fold, y_val_categorical),
        epochs=5,  # Epoch sedikit untuk contoh
        batch_size=BATCH_SIZE,
        verbose=1
    )
    
    print(f"  Training fold {current_fold} selesai")
    
    # LANGKAH 4.4: Evaluasi model
    print(f"Evaluasi model fold {current_fold}")
    
    # Evaluasi pada data validasi
    val_loss, val_accuracy = model.evaluate(X_val_fold, y_val_categorical, verbose=0)
    
    # Simpan hasil
    fold_accuracies.append(val_accuracy)
    fold_losses.append(val_loss)
    
    print(f"  Fold {current_fold} - Accuracy: {val_accuracy:.4f}, Loss: {val_loss:.4f}")
    
    # LANGKAH 4.5: Prediksi untuk analisis
    print(f"Membuat prediksi fold {current_fold}")
    
    # Prediksi pada data validasi
    predictions = model.predict(X_val_fold, verbose=0)
    predicted_classes = np.argmax(predictions, axis=1)
    true_classes = y_val_fold
    
    # Simpan untuk analisis keseluruhan
    all_predictions.extend(predicted_classes)
    all_true_labels.extend(true_classes)
    
    print(f"  Prediksi fold {current_fold} selesai")
    
    # LANGKAH 4.6: Bersihkan memory
    del model
    tf.keras.backend.clear_session()
    
    print(f"Fold {current_fold} selesai!")

print("\n=== SEMUA FOLD SELESAI ===")

# ===== LANGKAH 5: ANALISIS HASIL =====
print("\nLANGKAH 5: Analisis Hasil Cross Validation")

print("\nHasil setiap fold:")
for i, (acc, loss) in enumerate(zip(fold_accuracies, fold_losses)):
    print(f"  Fold {i+1}: Accuracy = {acc:.4f}, Loss = {loss:.4f}")

# Hitung statistik keseluruhan
mean_accuracy = np.mean(fold_accuracies)
std_accuracy = np.std(fold_accuracies)
mean_loss = np.mean(fold_losses)

print(f"\nRingkasan Statistik:")
print(f"  Mean Accuracy: {mean_accuracy:.4f} ± {std_accuracy:.4f}")
print(f"  Mean Loss: {mean_loss:.4f}")

# ===== LANGKAH 6: VISUALISASI SEDERHANA =====
print("\nLANGKAH 6: Membuat Visualisasi")

# Plot 1: Accuracy setiap fold
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
fold_numbers = list(range(1, N_FOLDS + 1))
plt.bar(fold_numbers, fold_accuracies, color='skyblue', alpha=0.7)
plt.axhline(y=mean_accuracy, color='red', linestyle='--', 
            label=f'Mean: {mean_accuracy:.4f}')
plt.xlabel('Fold')
plt.ylabel('Accuracy')
plt.title('Accuracy per Fold')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 2: Confusion Matrix sederhana
plt.subplot(1, 2, 2)
cm = confusion_matrix(all_true_labels, all_predictions)
plt.imshow(cm, interpolation='nearest', cmap='Blues')
plt.title('Confusion Matrix')
plt.colorbar()
plt.xlabel('Predicted')
plt.ylabel('Actual')

# Tambahkan angka di confusion matrix
for i in range(cm.shape[0]):
    for j in range(cm.shape[1]):
        plt.text(j, i, str(cm[i, j]), ha='center', va='center')

plt.tight_layout()
plt.show()

# ===== LANGKAH 7: KESIMPULAN =====
print("\nLANGKAH 7: Kesimpulan")

print("\n=== HASIL CROSS VALIDATION ===")
print(f"Jumlah fold yang diproses: {N_FOLDS}")
print(f"Total sampel yang dievaluasi: {len(all_true_labels)}")
print(f"Accuracy rata-rata: {mean_accuracy:.4f}")
print(f"Standard deviasi: {std_accuracy:.4f}")

# Hitung overall accuracy dari semua prediksi
overall_accuracy = accuracy_score(all_true_labels, all_predictions)
print(f"Overall accuracy: {overall_accuracy:.4f}")

print("\n=== CROSS VALIDATION PROCEDURAL SELESAI ===")
print("Semua langkah berhasil diselesaikan!")

# ===== CATATAN UNTUK IMPLEMENTASI NYATA =====
print("\n=== CATATAN UNTUK IMPLEMENTASI NYATA ===")
print("Untuk menggunakan dengan data sesungguhnya:")
print("1. Ganti bagian 'LANGKAH 2' dengan loading data gambar asli")
print("2. Tambahkan preprocessing gambar (resize, normalisasi)")
print("3. Sesuaikan parameter (epochs, batch_size, dll)")
print("4. Tambahkan data augmentation jika diperlukan")
print("5. Simpan model terbaik dari cross validation")
