#!/usr/bin/env python3
"""
Demo script untuk konversi gambar ke 224x224
"""

import os
import cv2
import numpy as np
from PIL import Image

# Buat sample data untuk demo
def create_demo_data():
    """Buat sample data untuk demo"""
    print("🔧 Membuat sample data untuk demo...")
    
    # Buat direktori demo
    demo_dir = "./demo_data"
    os.makedirs(demo_dir, exist_ok=True)
    
    # Buat subdirektori kelas
    classes = ["class1", "class2"]
    
    for class_name in classes:
        class_dir = os.path.join(demo_dir, class_name)
        os.makedirs(class_dir, exist_ok=True)
        
        # Buat 3 sample gambar dengan ukuran berbeda
        for i in range(3):
            if i == 0:
                size = (300, 200)  # Landscape
            elif i == 1:
                size = (150, 250)  # Portrait  
            else:
                size = (100, 100)  # Square
            
            # Buat gambar random berwarna
            image = np.random.randint(0, 255, (*size[::-1], 3), dtype=np.uint8)
            
            # Tambah lingkaran untuk membuat lebih menarik
            center = (size[0]//2, size[1]//2)
            radius = min(size)//4
            cv2.circle(image, center, radius, (255, 255, 0), -1)
            
            # Save
            filename = os.path.join(class_dir, f"sample_{i+1}.jpg")
            cv2.imwrite(filename, image)
    
    print(f"✅ Sample data dibuat di: {demo_dir}")
    return demo_dir

def convert_to_224x224(input_path, output_path):
    """Konversi gambar ke 224x224 dengan padding"""
    try:
        # Load gambar
        image = cv2.imread(input_path)
        if image is None:
            return False
        
        # Convert BGR ke RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image)
        
        # Get ukuran asli
        width, height = pil_image.size
        print(f"  📏 Original size: {width}x{height}")
        
        # Hitung scale
        scale = min(224 / width, 224 / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize
        resized = pil_image.resize((new_width, new_height), Image.LANCZOS)
        
        # Buat canvas 224x224 hitam
        result = Image.new('RGB', (224, 224), (0, 0, 0))
        
        # Center gambar
        x = (224 - new_width) // 2
        y = (224 - new_height) // 2
        result.paste(resized, (x, y))
        
        # Save
        result.save(output_path, 'JPEG', quality=95)
        print(f"  ✅ Converted to: 224x224")
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    print("🎯 Demo Image Conversion ke 224x224")
    print("=" * 40)
    
    # Cek apakah ada demo data
    demo_dir = "./demo_data"
    if not os.path.exists(demo_dir):
        demo_dir = create_demo_data()
    
    # Output directory
    output_dir = "./demo_data_224x224"
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n📁 Input: {demo_dir}")
    print(f"📁 Output: {output_dir}")
    print("-" * 40)
    
    total_converted = 0
    
    # Proses setiap kelas
    for class_name in os.listdir(demo_dir):
        class_input = os.path.join(demo_dir, class_name)
        
        if not os.path.isdir(class_input):
            continue
        
        print(f"\n📂 Processing: {class_name}")
        
        # Buat output directory
        class_output = os.path.join(output_dir, class_name)
        os.makedirs(class_output, exist_ok=True)
        
        # Proses setiap gambar
        for filename in os.listdir(class_input):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                input_file = os.path.join(class_input, filename)
                output_file = os.path.join(class_output, filename)
                
                print(f"  🔄 {filename}")
                if convert_to_224x224(input_file, output_file):
                    total_converted += 1
    
    print("\n" + "=" * 40)
    print(f"🎉 Demo selesai!")
    print(f"✅ Total converted: {total_converted} images")
    print(f"📁 Results saved to: {output_dir}")
    
    # Tampilkan cara penggunaan untuk data asli
    print("\n📋 Untuk data asli Anda:")
    print("1. Ganti 'demo_data' dengan path data Anda")
    print("2. Struktur direktori harus seperti:")
    print("   data/")
    print("     ├── Eurasian Tree Sparrow - Passer montanus/")
    print("     ├── Javan Munia - Lonchura leucogastroides/")
    print("     ├── Scaly-breasted Munia - Lonchura punctulata/")
    print("     └── White-headed Munia - Lonchura maja/")
    print("3. Jalankan script ini")
    print("4. Update path di notebook ke hasil konversi")

if __name__ == "__main__":
    main()
