#!/usr/bin/env python3
"""
Batch Rename Script - Rename semua folder dalam direktori sekaligus
Cocok untuk dataset dengan banyak kelas/kategori
"""

import os
from pathlib import Path
import time

def batch_rename_all_folders(base_directory, start_number=1, reset_numbering=True):
    """
    Rename semua file dalam semua subfolder dengan nama folder masing-masing

    Args:
        base_directory (str): Directory yang berisi folder-folder
        start_number (int): Nomor awal untuk setiap folder
        reset_numbering (bool): Reset nomor untuk setiap folder (True) atau lanjut (False)
    """

    base_path = Path(base_directory)

    if not base_path.exists():
        print(f"❌ Directory '{base_directory}' tidak ditemukan!")
        return

    # Dapatkan semua subfolder
    subfolders = [f for f in base_path.iterdir() if f.is_dir()]

    if not subfolders:
        print(f"📁 Tidak ada subfolder dalam '{base_directory}'!")
        return

    # Urutkan folder
    subfolders.sort(key=lambda x: x.name.lower())

    print(f"🔄 BATCH RENAME - SETIAP FOLDER DENGAN NAMA SENDIRI")
    print("=" * 60)
    print(f"📁 Base directory: {base_path}")
    print(f"📊 Total folder: {len(subfolders)}")
    print(f"🔢 Start number: {start_number}")
    print(f"🔄 Reset numbering per folder: {'Ya' if reset_numbering else 'Tidak'}")

    # Tampilkan preview dengan nama yang akan digunakan
    print(f"\n📋 Folder yang akan diproses:")
    total_files = 0
    for folder in subfolders:
        file_count = len([f for f in folder.iterdir() if f.is_file()])
        total_files += file_count
        print(f"  📁 {folder.name} ({file_count} files) → akan jadi '{folder.name} 1', '{folder.name} 2', dst.")

    print(f"\n📊 Total file keseluruhan: {total_files}")

    # Konfirmasi
    confirm = input(f"\n❓ Lanjutkan batch rename? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', 'ya']:
        print("❌ Batch rename dibatalkan.")
        return

    print(f"\n🔄 Memulai batch rename...")
    print("=" * 60)

    total_success = 0
    total_failed = 0
    current_number = start_number

    for folder_idx, folder in enumerate(subfolders, 1):
        folder_name = folder.name
        files = [f for f in folder.iterdir() if f.is_file()]

        if not files:
            print(f"📁 [{folder_idx}/{len(subfolders)}] {folder_name} - KOSONG")
            continue

        print(f"📁 [{folder_idx}/{len(subfolders)}] {folder_name} ({len(files)} files)")
        print(f"    🏷️  Base name: '{folder_name}'")

        # Urutkan file dalam folder
        files.sort(key=lambda x: x.name.lower())

        folder_success = 0
        folder_failed = 0

        # Reset nomor untuk setiap folder jika diminta
        if reset_numbering:
            file_start_number = start_number
        else:
            file_start_number = current_number

        for file_idx, file_path in enumerate(files):
            try:
                # Dapatkan ekstensi
                extension = file_path.suffix

                # Nomor file
                file_number = file_start_number + file_idx

                # Nama baru menggunakan nama folder
                new_name = f"{folder_name} {file_number}{extension}"
                new_path = folder / new_name

                # Skip jika sudah sesuai format
                if file_path.name == new_name:
                    print(f"    ⏭️  {file_path.name} (sudah sesuai)")
                    folder_success += 1
                    continue

                # Cek konflik nama
                if new_path.exists():
                    print(f"    ❌ {file_path.name} → KONFLIK: {new_name} sudah ada")
                    folder_failed += 1
                    continue

                # Rename
                file_path.rename(new_path)
                print(f"    ✅ {file_path.name} → {new_name}")
                folder_success += 1

            except Exception as e:
                print(f"    ❌ ERROR: {file_path.name} - {e}")
                folder_failed += 1

        # Update current_number jika tidak reset per folder
        if not reset_numbering:
            current_number += len(files)

        total_success += folder_success
        total_failed += folder_failed

        print(f"    📊 Folder summary: ✅{folder_success} ❌{folder_failed}")
        print()

        # Jeda kecil untuk readability
        time.sleep(0.1)

    # Summary akhir
    print("=" * 60)
    print(f"🎉 BATCH RENAME SELESAI!")
    print(f"   📊 Total file berhasil: {total_success}")
    print(f"   ❌ Total file gagal: {total_failed}")
    print(f"   📁 Total folder diproses: {len(subfolders)}")

    # Tampilkan contoh hasil
    print(f"\n📋 Contoh hasil:")
    for folder in subfolders[:3]:  # Tampilkan 3 folder pertama
        files = [f for f in folder.iterdir() if f.is_file()]
        if files:
            print(f"   📁 {folder.name}:")
            for i, file_path in enumerate(files[:2], start_number):  # 2 file pertama
                extension = file_path.suffix
                print(f"      → {folder.name} {i}{extension}")
            if len(files) > 2:
                print(f"      → ... dan {len(files)-2} file lainnya")

def preview_batch_rename(base_directory, start_number=1, reset_numbering=True):
    """Preview batch rename tanpa melakukan perubahan"""

    base_path = Path(base_directory)

    if not base_path.exists():
        print(f"❌ Directory '{base_directory}' tidak ditemukan!")
        return

    subfolders = [f for f in base_path.iterdir() if f.is_dir()]
    subfolders.sort(key=lambda x: x.name.lower())

    print(f"🔍 PREVIEW BATCH RENAME - SETIAP FOLDER DENGAN NAMA SENDIRI")
    print("=" * 60)
    print(f"📁 Base directory: {base_path}")
    print(f"📊 Total folder: {len(subfolders)}")
    print(f"🔢 Start number: {start_number}")
    print(f"🔄 Reset numbering per folder: {'Ya' if reset_numbering else 'Tidak'}")

    current_number = start_number

    for folder in subfolders:
        files = [f for f in folder.iterdir() if f.is_file()]
        if not files:
            continue

        files.sort(key=lambda x: x.name.lower())
        print(f"\n📁 {folder.name} ({len(files)} files):")
        print(f"    🏷️  Base name: '{folder.name}'")

        # Tentukan nomor awal untuk folder ini
        if reset_numbering:
            file_start_number = start_number
        else:
            file_start_number = current_number

        # Tampilkan beberapa contoh saja
        for i, file_path in enumerate(files[:5]):
            extension = file_path.suffix
            file_number = file_start_number + i
            new_name = f"{folder.name} {file_number}{extension}"
            print(f"    {file_path.name} → {new_name}")

        if len(files) > 5:
            print(f"    ... dan {len(files)-5} file lainnya")

        # Update current_number jika tidak reset per folder
        if not reset_numbering:
            current_number += len(files)

if __name__ == "__main__":
    print("🔄 BATCH RENAME SCRIPT")
    print("=" * 50)
    
    # Contoh direktori yang umum
    common_dirs = [
        "ResizedDatasetUnknown",
        "ResizedDataset", 
        "Dataset",
        "raw_data"
    ]
    
    print("Pilih directory:")
    for i, dir_path in enumerate(common_dirs, 1):
        if Path(dir_path).exists():
            subfolder_count = len([f for f in Path(dir_path).iterdir() if f.is_dir()])
            print(f"  {i}. {dir_path} ({subfolder_count} folders)")
        else:
            print(f"  {i}. {dir_path} (tidak ditemukan)")

    print(f"  {len(common_dirs)+1}. Input manual")
    print(f"  {len(common_dirs)+2}. Preview mode")

    try:
        choice = int(input(f"\nPilih (1-{len(common_dirs)+2}): "))

        if 1 <= choice <= len(common_dirs):
            selected_dir = common_dirs[choice-1]

            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1

            reset_choice = input("🔄 Reset nomor untuk setiap folder? (y/n, default: y): ").strip().lower()
            reset_numbering = reset_choice in ['', 'y', 'yes', 'ya']

            batch_rename_all_folders(selected_dir, start_num, reset_numbering)

        elif choice == len(common_dirs)+1:
            dir_path = input("📁 Masukkan path directory: ").strip().strip('"')
            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1

            reset_choice = input("🔄 Reset nomor untuk setiap folder? (y/n, default: y): ").strip().lower()
            reset_numbering = reset_choice in ['', 'y', 'yes', 'ya']

            batch_rename_all_folders(dir_path, start_num, reset_numbering)

        elif choice == len(common_dirs)+2:
            dir_path = input("📁 Masukkan path directory untuk preview: ").strip().strip('"')
            start_num = input("🔢 Nomor awal (default: 1): ").strip()
            start_num = int(start_num) if start_num else 1

            reset_choice = input("🔄 Reset nomor untuk setiap folder? (y/n, default: y): ").strip().lower()
            reset_numbering = reset_choice in ['', 'y', 'yes', 'ya']

            preview_batch_rename(dir_path, start_num, reset_numbering)
        else:
            print("❌ Pilihan tidak valid!")

    except (ValueError, KeyboardInterrupt):
        print("\n❌ Program dibatalkan.")
