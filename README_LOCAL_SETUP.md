# 🐦 Setup Data Lokal untuk MobileNetV2 Bird Classification

## 📋 <PERSON><PERSON><PERSON>

Saya telah membuat kode yang akan otomatis mendeteksi dan menggunakan data lokal yang sudah Anda miliki. Tidak perlu lagi menggunakan Google Drive atau Colab!

## 📁 Data yang Terdeteksi

Berdasarkan struktur direktori Anda, script akan otomatis mencari dan menggunakan:

1. **`./ResizedDataset`** ✅ (PRIORITAS UTAMA - data sudah 224x224)
2. **`./Dataset`** ✅ (data asli)
3. **`./data`** (direktori alternatif)
4. **`./processed_data`** (hasil preprocessing)

## 🚀 Cara Menggunakan

### Opsi 1: Copy-Paste ke Notebook (RECOMMENDED)

1. **Buka notebook Anda** (`Klasifikasi_burung_mobilenetv2.ipynb`)

2. **Ganti cell pertama** dengan kode dari file `local_notebook_code.py`:
   ```python
   # Copy seluruh isi file local_notebook_code.py ke cell pertama notebook
   ```

3. **Ganti cell model dan training** dengan kode dari file `model_training_code.py`:
   ```python
   # Copy seluruh isi file model_training_code.py ke cell setelah setup data
   ```

4. **Jalankan notebook** seperti biasa!

### Opsi 2: Jalankan Script Langsung

```bash
# Jalankan script training lengkap
python local_training_script.py
```

### Opsi 3: Setup Otomatis

```bash
# Setup konfigurasi otomatis
python setup_local_data.py
```

## 📊 Apa yang Akan Terjadi

1. **Deteksi Otomatis**: Script akan mencari direktori data yang tersedia
2. **Prioritas Cerdas**: Akan menggunakan `ResizedDataset` jika ada (karena sudah 224x224)
3. **Info Lengkap**: Menampilkan jumlah kelas dan gambar per kelas
4. **Konfigurasi Optimal**: Batch size dan parameter disesuaikan untuk environment lokal

## 🎯 Konfigurasi yang Digunakan

- **Data Directory**: Otomatis terdeteksi (`ResizedDataset` atau `Dataset`)
- **Batch Size**: 32 (disesuaikan untuk lokal)
- **Validation Split**: 30%
- **Input Size**: 224x224x3
- **Classes**: Otomatis terdeteksi dari subdirektori

## 📈 Output yang Dihasilkan

Setelah training selesai, Anda akan mendapatkan:

- `best_birds_model.h5` - Model terbaik selama training
- `birds_classification_local.h5` - Model final
- `birds_model_savedmodel/` - Format SavedModel untuk deployment
- `training_history.png` - Grafik training
- `confusion_matrix.png` - Confusion matrix

## 🔧 Kustomisasi

Jika ingin mengubah konfigurasi, edit bagian ini di kode:

```python
# Batch size
batch_size = 32  # Ubah sesuai kebutuhan

# Validation split
validation_split = 0.3  # 30% untuk validation

# Epochs
epochs = 30  # Jumlah epoch training
```

## 🐛 Troubleshooting

### Problem: "Tidak ditemukan dataset yang valid"
**Solusi**: Pastikan struktur direktori seperti ini:
```
ResizedDataset/ (atau Dataset/)
├── Lonchura leucogastroides/
├── Lonchura maja/
├── Lonchura punctulata/
└── Passer montanus/
```

### Problem: "Out of memory"
**Solusi**: Kurangi batch size:
```python
batch_size = 16  # atau 8
```

### Problem: Training terlalu lambat
**Solusi**: 
1. Gunakan `ResizedDataset` (data sudah 224x224)
2. Kurangi epochs
3. Pastikan GPU terdeteksi

## 📞 Bantuan

Jika ada masalah:

1. **Cek struktur data**: Pastikan ada gambar di setiap subdirektori
2. **Cek dependencies**: Pastikan TensorFlow, OpenCV, PIL terinstall
3. **Cek memory**: Monitor penggunaan RAM/GPU

## 🎉 Keunggulan Setup Ini

✅ **Otomatis**: Deteksi data tanpa konfigurasi manual  
✅ **Fleksibel**: Bisa menggunakan berbagai struktur direktori  
✅ **Optimal**: Konfigurasi disesuaikan untuk environment lokal  
✅ **Lengkap**: Termasuk visualisasi dan evaluasi  
✅ **Siap Pakai**: Langsung bisa digunakan untuk prediksi  

---

**Happy Training! 🚀**
