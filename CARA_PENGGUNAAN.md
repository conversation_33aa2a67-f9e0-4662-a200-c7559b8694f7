# 📋 Cara Menggunakan Image Converter 224x224

## 🎯 Tujuan
Script ini mengkonversi gambar dengan berbagai ukuran menjadi **224x224 pixels** yang dibutuhkan untuk training MobileNetV2.

## 📁 Persiapan Data

### 1. Struktur Direktori Input
Buat struktur direktori seperti ini:
```
data/
├── Eurasian Tree Sparrow - Passer montanus/
│   ├── gambar1.jpg
│   ├── gambar2.png
│   └── ...
├── Javan Munia - Lonchura leucogastroides/
│   ├── gambar3.jpg
│   ├── gambar4.jpeg
│   └── ...
├── Scaly-breasted Munia - Lonchura punctulata/
│   ├── gambar5.jpg
│   └── ...
└── White-headed Munia - Lonchura maja/
    ├── gambar6.png
    └── ...
```

### 2. Format Gambar yang Didukung
- ✅ JPG/JPEG
- ✅ PNG  
- ✅ BMP
- ✅ TIFF

## 🚀 Cara Penggunaan

### Opsi 1: Script Sederhana (Recommended)
```bash
python resize_images.py
```

**Langkah-langkah:**
1. Jalankan script
2. Masukkan path direktori input (atau tekan Enter untuk default `./data`)
3. Masukkan path direktori output (atau tekan Enter untuk default `./data_224x224`)
4. Script akan otomatis memproses semua gambar

### Opsi 2: Script Interaktif
```bash
python simple_convert_224.py
```

### Opsi 3: Script dengan Command Line Arguments
```bash
python convert_images_224x224.py --input ./data --output ./data_224x224 --method padding
```

## ⚙️ Metode Konversi

### Method: Padding (Recommended)
- ✅ **Mempertahankan aspect ratio**
- ✅ **Tidak ada distorsi gambar**
- ✅ **Menambah padding hitam untuk mencapai 224x224**

**Contoh:**
- Gambar 300x200 → Resize ke 224x149 → Tambah padding atas/bawah → 224x224
- Gambar 150x300 → Resize ke 112x224 → Tambah padding kiri/kanan → 224x224

### Method: Stretch
- ⚠️ **Mengubah aspect ratio**
- ⚠️ **Gambar bisa terdistorsi**
- ✅ **Lebih cepat**

## 📊 Output

### Struktur Output
```
data_224x224/
├── Eurasian Tree Sparrow - Passer montanus/
│   ├── gambar1.jpg (224x224)
│   ├── gambar2.jpg (224x224)
│   └── ...
├── Javan Munia - Lonchura leucogastroides/
│   ├── gambar3.jpg (224x224)
│   └── ...
└── ...
```

### Informasi yang Ditampilkan
- ✅ Progress per kelas
- ✅ Jumlah gambar berhasil/gagal
- ✅ Success rate
- ✅ Path output directory

## 🔧 Penggunaan dengan Notebook

### 1. Setelah Konversi Selesai
Update path data di notebook Anda:

```python
# Ganti path ini
main_data_dir = './data_224x224'  # Path ke gambar yang sudah dikonversi

# Atau jika menggunakan path absolut
main_data_dir = 'C:/path/to/your/data_224x224'
```

### 2. Data Generator
```python
train_generator = datagen.flow_from_directory(
    main_data_dir,
    target_size=(224, 224),  # Sudah 224x224, tapi tetap specify
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=True,
    subset="training"
)
```

## 🛠️ Troubleshooting

### Error: "Direktori tidak ditemukan"
**Solusi:** Pastikan path direktori input benar dan ada

### Error: "Tidak bisa membaca gambar"
**Solusi:** 
- Cek format file (harus JPG/PNG/BMP/TIFF)
- Pastikan file tidak corrupt
- Cek permission file

### Error: "No module named 'cv2'"
**Solusi:** Install OpenCV
```bash
pip install opencv-python
```

### Memory Error
**Solusi:**
- Proses dataset dalam batch kecil
- Tutup aplikasi lain yang menggunakan memory
- Gunakan komputer dengan RAM lebih besar

## 📈 Tips Optimasi

### 1. **Kualitas vs Speed**
- Gunakan **padding method** untuk kualitas terbaik
- Gunakan **stretch method** untuk speed terbaik

### 2. **Storage**
- Output format selalu JPG dengan quality 95%
- Ukuran file akan konsisten karena semua 224x224

### 3. **Batch Processing**
- Script otomatis memproses semua subdirektori
- Progress ditampilkan setiap 10 gambar

## ✅ Checklist Sebelum Training

- [ ] Semua gambar sudah dikonversi ke 224x224
- [ ] Struktur direktori output benar
- [ ] Tidak ada error saat konversi
- [ ] Path di notebook sudah diupdate
- [ ] Data generator bisa load data dengan benar

## 🎉 Hasil Akhir

Setelah konversi berhasil:
1. ✅ Semua gambar berukuran 224x224
2. ✅ Aspect ratio terjaga (jika pakai padding)
3. ✅ Siap untuk training MobileNetV2
4. ✅ Konsistensi input terjamin

---

**🚀 Selamat! Data Anda siap untuk training MobileNetV2!**
