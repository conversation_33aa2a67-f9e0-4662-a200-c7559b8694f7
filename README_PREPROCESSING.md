# Image Preprocessing untuk MobileNetV2 Bird Classification

## 📋 Overview

Notebook `Klasifikasi_burung_mobilenetv2.ipynb` telah dimodifikasi untuk menyertakan **preprocessing otomatis** yang mengkonversi semua gambar input menjadi ukuran **224x224 pixels** sebelum digunakan dalam data generator. Ini memastikan konsistensi input untuk model MobileNetV2.

## 🔧 Fitur yang Ditambahkan

### 1. **Import Libraries Tambahan**
```python
import cv2
from PIL import Image
from pathlib import Path
```

### 2. **Fungsi Preprocessing**

#### `resize_with_padding(image, target_size=(224, 224), pad_color=(0, 0, 0))`
- **Fungsi**: Resize gambar dengan mempertahankan aspect ratio
- **Method**: Menambahkan padding (default: hitam) untuk mencapai ukuran target
- **Keuntungan**: Tidak ada distorsi gambar, kualitas tetap terjaga

#### `resize_with_stretch(image, target_size=(224, 224))`
- **Fungsi**: Resize gambar dengan stretching langsung
- **Method**: Mengubah ukuran tanpa mempertahankan aspect ratio
- **Keuntungan**: Lebih cepat, tidak ada padding

#### `preprocess_dataset(input_dir, output_dir, target_size=(224, 224), method='padding')`
- **Fungsi**: Memproses seluruh dataset secara batch
- **Input**: Direktori dengan gambar berbagai ukuran
- **Output**: Direktori dengan semua gambar berukuran 224x224
- **Progress**: Menampilkan progress dan statistik

### 3. **Struktur Direktori yang Direkomendasikan**

```
project_folder/
├── raw_data/                          # Gambar asli dengan berbagai ukuran
│   ├── Eurasian Tree Sparrow - Passer montanus/
│   ├── Javan Munia - Lonchura leucogastroides/
│   ├── Scaly-breasted Munia - Lonchura punctulata/
│   └── White-headed Munia - Lonchura maja/
├── processed_data/                    # Gambar yang sudah diproses ke 224x224
│   ├── Eurasian Tree Sparrow - Passer montanus/
│   ├── Javan Munia - Lonchura leucogastroides/
│   ├── Scaly-breasted Munia - Lonchura punctulata/
│   └── White-headed Munia - Lonchura maja/
└── Klasifikasi_burung_mobilenetv2.ipynb
```

### 4. **Workflow Otomatis**

1. **Deteksi Data**: Notebook otomatis mencari direktori `raw_data`
2. **Preprocessing**: Jika `processed_data` belum ada, otomatis memproses gambar
3. **Validasi**: Menampilkan statistik dan info dataset
4. **Data Generator**: Menggunakan data yang sudah diproses untuk training

## 🚀 Cara Penggunaan

### Step 1: Persiapan Data
```bash
# Buat struktur direktori
mkdir raw_data
mkdir raw_data/"Eurasian Tree Sparrow - Passer montanus"
mkdir raw_data/"Javan Munia - Lonchura leucogastroides"
mkdir raw_data/"Scaly-breasted Munia - Lonchura punctulata"
mkdir raw_data/"White-headed Munia - Lonchura maja"

# Copy gambar ke direktori yang sesuai
```

### Step 2: Jalankan Notebook
1. Buka `Klasifikasi_burung_mobilenetv2.ipynb`
2. Jalankan cell pertama (imports)
3. Jalankan cell preprocessing - akan otomatis:
   - Mendeteksi raw data
   - Memproses gambar ke 224x224
   - Membuat direktori processed_data
   - Menampilkan progress dan statistik

### Step 3: Lanjutkan Training
- Data generator akan otomatis menggunakan gambar yang sudah diproses
- Model MobileNetV2 akan menerima input yang konsisten (224x224)

## ⚙️ Konfigurasi

### Pilihan Method Preprocessing
```python
preprocessing_method = 'padding'  # Recommended
# atau
preprocessing_method = 'stretch'
```

### Kustomisasi Path
```python
raw_data_dir = './raw_data'           # Sesuaikan dengan lokasi data asli
processed_data_dir = './processed_data'  # Sesuaikan dengan lokasi output
```

### Batch Size
```python
batch_size = 32  # Disesuaikan untuk environment lokal
```

## 📊 Keuntungan Preprocessing

### 1. **Konsistensi Input**
- Semua gambar memiliki ukuran yang sama (224x224)
- Model MobileNetV2 menerima input yang optimal
- Tidak ada error karena ukuran gambar yang berbeda

### 2. **Kualitas Gambar**
- Method 'padding' mempertahankan aspect ratio
- Tidak ada distorsi pada gambar
- Kualitas visual tetap terjaga

### 3. **Efisiensi Training**
- Preprocessing dilakukan sekali di awal
- Data generator lebih cepat karena tidak perlu resize real-time
- Memory usage lebih predictable

### 4. **Fleksibilitas**
- Support berbagai format gambar (JPG, PNG, BMP, TIFF)
- Bisa memilih method preprocessing
- Easy to customize

## 🔍 Visualisasi

Notebook menyertakan cell untuk visualisasi:
- Sample gambar yang sudah diproses
- Perbandingan sebelum dan sesudah preprocessing
- Distribusi kelas dan jumlah gambar

## 🛠️ Troubleshooting

### Error: "Raw data directory tidak ditemukan"
**Solusi**: Pastikan direktori `raw_data` ada dan berisi subdirektori kelas

### Error: "No module named 'cv2'"
**Solusi**: Install OpenCV
```bash
pip install opencv-python
```

### Error: "No module named 'PIL'"
**Solusi**: Install Pillow
```bash
pip install pillow
```

### Memory Error saat Preprocessing
**Solusi**: 
- Proses dataset dalam batch kecil
- Reduce image quality saat save
- Gunakan format JPEG dengan compression

## 📈 Performance Tips

1. **Gunakan SSD** untuk storage data jika memungkinkan
2. **Batch size** sesuaikan dengan RAM yang tersedia
3. **Method 'padding'** untuk kualitas terbaik
4. **Method 'stretch'** untuk speed terbaik
5. **Monitor memory usage** saat preprocessing dataset besar

## 🎯 Next Steps

Setelah preprocessing selesai:
1. Jalankan training dengan data yang sudah diproses
2. Monitor training metrics
3. Evaluate model performance
4. Fine-tune hyperparameters jika diperlukan

---

**✅ Preprocessing integration berhasil ditambahkan ke notebook!**
**🚀 Siap untuk training dengan input yang konsisten 224x224!**
